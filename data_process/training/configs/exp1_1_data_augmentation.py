#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实验1.1: 数据增强优化配置
目标: 通过时间域数据增强解决数据集过小的问题
预期效果: 提高模型泛化能力，改善AR@1/5/10指标
"""

_base_ = [
    '../../../mmaction2/configs/_base_/default_runtime.py'
]

# 导入自定义变换模块
import fix_transforms  # noqa: F401

# 模型设置 - 保持基础配置不变
model = dict(
    type='BMN',
    temporal_dim=100,           # 时间维度：100个时间步
    boundary_ratio=0.5,
    num_samples=32,
    num_samples_per_bin=3,
    feat_dim=2048,              # SlowOnly R50特征维度
    soft_nms_alpha=0.4,
    soft_nms_low_threshold=0.5,
    soft_nms_high_threshold=0.9,
    post_process_top_k=100)

# 数据集设置
dataset_type = 'ActivityNetDataset'
data_root = '../../../data/MultiClassTAD/features_slowonly'
data_root_val = '../../../data/MultiClassTAD/features_slowonly'
ann_file_train = '../../../data/MultiClassTAD/multiclass_tad_train.json'
ann_file_val = '../../../data/MultiClassTAD/multiclass_tad_val.json'
ann_file_test = '../../../data/MultiClassTAD/multiclass_tad_val.json'

# 训练数据管道 - 添加数据增强
train_pipeline = [
    dict(type='LoadLocalizationFeatureForBMN'),
    dict(
        type='TemporalAugmentation',
        temporal_crop_ratio=0.15,      # 增加裁剪比例
        feature_noise_std=0.02,        # 增加噪声强度
        feature_scale_range=(0.85, 1.15),  # 扩大缩放范围
        prob=0.7                       # 提高增强概率
    ),
    dict(type='GenerateLocalizationLabels'),
    dict(
        type='PackLocalizationInputs',
        keys=('gt_bbox', ),
        meta_keys=('video_name', ))
]

# 验证数据管道 - 不使用增强
val_pipeline = [
    dict(type='LoadLocalizationFeatureForBMN'),
    dict(type='GenerateLocalizationLabels'),
    dict(
        type='PackLocalizationInputs',
        keys=('gt_bbox', ),
        meta_keys=('video_name', 'duration_second', 'duration_frame',
                   'annotations', 'feature_frame'))
]

# 测试数据管道
test_pipeline = [
    dict(type='LoadLocalizationFeatureForBMN'),
    dict(
        type='PackLocalizationInputs',
        keys=('gt_bbox', ),
        meta_keys=('video_name', 'duration_second', 'duration_frame',
                   'annotations', 'feature_frame'))
]

# 数据加载器配置 - 增加批次大小
train_dataloader = dict(
    batch_size=8,                    # 从4增加到8
    num_workers=4,
    persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=True),
    drop_last=True,
    dataset=dict(
        type=dataset_type,
        ann_file=ann_file_train,
        data_prefix=dict(video=data_root),
        pipeline=train_pipeline))

val_dataloader = dict(
    batch_size=1,
    num_workers=4,
    persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=False),
    dataset=dict(
        type=dataset_type,
        ann_file=ann_file_val,
        data_prefix=dict(video=data_root_val),
        pipeline=val_pipeline,
        test_mode=True))

test_dataloader = dict(
    batch_size=1,
    num_workers=4,
    persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=False),
    dataset=dict(
        type=dataset_type,
        ann_file=ann_file_test,
        data_prefix=dict(video=data_root_val),
        pipeline=test_pipeline,
        test_mode=True))

# 评估器设置
val_evaluator = dict(
    type='ANetMetric',
    metric_type='AR@AN',
    dump_config=dict(
        out='../../../work_dirs/bmn_exp1_1_data_aug/results.json',
        output_format='json'))
test_evaluator = val_evaluator

# 训练配置 - 增加训练轮数
max_epochs = 30                      # 从20增加到30
train_cfg = dict(
    type='EpochBasedTrainLoop',
    max_epochs=max_epochs,
    val_begin=1,
    val_interval=2)                  # 每2轮验证一次

val_cfg = dict(type='ValLoop')
test_cfg = dict(type='TestLoop')

# 学习率调度 - 更温和的衰减
param_scheduler = [
    dict(
        type='MultiStepLR',
        begin=0,
        end=max_epochs,
        by_epoch=True,
        milestones=[20, 25],         # 调整衰减时机
        gamma=0.5)                   # 更温和的衰减
]

# 优化器配置 - 降低初始学习率
optim_wrapper = dict(
    type='OptimWrapper',
    optimizer=dict(
        type='Adam',
        lr=0.0005,                   # 从0.001降到0.0005
        weight_decay=0.0002),        # 增加权重衰减
    clip_grad=dict(max_norm=40, norm_type=2))

# 工作目录
work_dir = '../../../work_dirs/bmn_exp1_1_data_aug'

# 钩子配置
default_hooks = dict(
    checkpoint=dict(
        type='CheckpointHook',
        interval=5,
        save_best='auto',
        max_keep_ckpts=3),
    logger=dict(
        type='LoggerHook',
        ignore_last=False,
        interval=10),
    param_scheduler=dict(type='ParamSchedulerHook'),
    runtime_info=dict(type='RuntimeInfoHook'),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    sync_buffers=dict(type='SyncBuffersHook'),
    timer=dict(type='IterTimerHook'))

# 环境配置
env_cfg = dict(
    cudnn_benchmark=False,
    dist_cfg=dict(backend='nccl'),
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=0))

# 日志配置
log_processor = dict(by_epoch=True, type='LogProcessor', window_size=20)
log_level = 'INFO'

# 可视化配置
vis_backends = [dict(type='LocalVisBackend')]
visualizer = dict(type='ActionVisualizer', vis_backends=vis_backends)

# 其他配置
default_scope = 'mmaction'
launcher = 'none'
load_from = None
resume = False
randomness = dict(deterministic=False, diff_rank_seed=False, seed=None)

# 实验配置说明
config_info = {
    'experiment': '1.1 数据增强优化',
    'changes': [
        '添加TemporalAugmentation数据增强',
        '批次大小从4增加到8',
        '训练轮数从20增加到30',
        '学习率从0.001降到0.0005',
        '更温和的学习率衰减策略'
    ],
    'expected_improvements': [
        '提高模型泛化能力',
        '改善AR@1/5/10指标',
        '减少过拟合风险'
    ],
    'success_criteria': [
        'AR@1 > 0.01',
        'AR@5 > 0.05', 
        'AR@10 > 0.10',
        'AUC保持在2.0以上'
    ]
}
