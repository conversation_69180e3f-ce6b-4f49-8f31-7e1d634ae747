#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复BMN输入维度的变换
"""

import numpy as np
from mmcv.transforms import BaseTransform
from mmaction.registry import TRANSFORMS


@TRANSFORMS.register_module()
class LoadLocalizationFeatureForBMN(BaseTransform):
    """
    为BMN模型加载时间动作定位特征文件

    BMN期望的输入格式是 [batch_size, feat_dim, temporal_dim]
    但标准的LoadLocalizationFeature产生的是 [temporal_dim, feat_dim]

    这个变换直接加载CSV文件而不进行转置，以匹配BMN的期望格式。

    Args:
        raw_feature_ext (str): 特征文件扩展名。默认: '.csv'
    """

    def __init__(self, raw_feature_ext='.csv'):
        self.raw_feature_ext = raw_feature_ext

    def transform(self, results):
        """
        执行特征加载变换

        Args:
            results (dict): 包含数据信息的字典，需要包含'feature_path'键

        Returns:
            dict: 添加了'raw_feature'键的结果字典
        """
        data_path = results['feature_path']

        # 加载CSV文件
        raw_feature = np.loadtxt(
            data_path, dtype=np.float32, delimiter=',', skiprows=1)

        # 不进行转置，保持原始格式 (feat_dim, temporal_dim)
        # 这样批处理后会变成 [batch_size, feat_dim, temporal_dim]，符合BMN期望
        results['raw_feature'] = raw_feature

        return results

    def __repr__(self):
        repr_str = f'{self.__class__.__name__}(raw_feature_ext={self.raw_feature_ext})'
        return repr_str


@TRANSFORMS.register_module()
class TemporalAugmentation(BaseTransform):
    """
    时间域数据增强变换，用于BMN模型训练

    包括时间裁剪、特征缩放和噪声添加等增强技术

    Args:
        temporal_crop_ratio (float): 时间裁剪比例范围。默认: 0.1
        feature_noise_std (float): 特征噪声标准差。默认: 0.01
        feature_scale_range (tuple): 特征缩放范围。默认: (0.9, 1.1)
        prob (float): 应用增强的概率。默认: 0.5
    """

    def __init__(self,
                 temporal_crop_ratio=0.1,
                 feature_noise_std=0.01,
                 feature_scale_range=(0.9, 1.1),
                 prob=0.5):
        self.temporal_crop_ratio = temporal_crop_ratio
        self.feature_noise_std = feature_noise_std
        self.feature_scale_range = feature_scale_range
        self.prob = prob

    def transform(self, results):
        """
        执行时间域数据增强

        Args:
            results (dict): 包含'raw_feature'的结果字典

        Returns:
            dict: 增强后的结果字典
        """
        if np.random.random() > self.prob:
            return results

        raw_feature = results['raw_feature']
        feat_dim, temporal_dim = raw_feature.shape

        # 1. 时间裁剪增强
        if np.random.random() < 0.3:
            crop_size = int(temporal_dim * (1 - self.temporal_crop_ratio))
            start_idx = np.random.randint(0, temporal_dim - crop_size + 1)
            cropped_feature = raw_feature[:, start_idx:start_idx + crop_size]

            # 插值回原始长度
            from scipy import interpolate
            x_old = np.linspace(0, 1, crop_size)
            x_new = np.linspace(0, 1, temporal_dim)

            augmented_feature = np.zeros_like(raw_feature)
            for i in range(feat_dim):
                f = interpolate.interp1d(x_old, cropped_feature[i], kind='linear')
                augmented_feature[i] = f(x_new)

            raw_feature = augmented_feature

        # 2. 特征缩放增强
        if np.random.random() < 0.3:
            scale_factor = np.random.uniform(*self.feature_scale_range)
            raw_feature = raw_feature * scale_factor

        # 3. 噪声添加增强
        if np.random.random() < 0.3:
            noise = np.random.normal(0, self.feature_noise_std, raw_feature.shape)
            raw_feature = raw_feature + noise.astype(np.float32)

        results['raw_feature'] = raw_feature
        return results

    def __repr__(self):
        repr_str = (f'{self.__class__.__name__}('
                   f'temporal_crop_ratio={self.temporal_crop_ratio}, '
                   f'feature_noise_std={self.feature_noise_std}, '
                   f'feature_scale_range={self.feature_scale_range}, '
                   f'prob={self.prob})')
        return repr_str


@TRANSFORMS.register_module()
class TransposeFeature(BaseTransform):
    """
    转置特征矩阵的维度
    
    Args:
        axes (tuple): 转置的轴顺序，默认为 (1, 0) 即交换前两个维度
    """
    
    def __init__(self, axes=(1, 0)):
        self.axes = axes
    
    def transform(self, results):
        """
        执行特征转置
        
        Args:
            results (dict): 包含数据信息的字典，需要包含'raw_feature'键
            
        Returns:
            dict: 修改了'raw_feature'的结果字典
        """
        if 'raw_feature' not in results:
            raise KeyError("results中缺少'raw_feature'键")
        
        # 转置特征矩阵
        results['raw_feature'] = np.transpose(results['raw_feature'], self.axes)
        
        return results
    
    def __repr__(self):
        repr_str = f'{self.__class__.__name__}(axes={self.axes})'
        return repr_str
