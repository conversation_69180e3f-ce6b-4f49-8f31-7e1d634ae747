#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实验2.1: 边界参数优化配置
目标: 优化boundary_ratio和num_samples参数，提高边界检测精度
预期效果: 显著改善AR@1/5/10指标，提高精确定位能力
"""

# 基于原始配置文件
_base_ = ['./bmn_multiclass_tad_config.py']

# 模型设置 - 优化边界检测参数
model = dict(
    boundary_ratio=0.3,              # 从0.5降到0.3，提高边界敏感度
    num_samples=64,                  # 从32增到64，增加采样密度
    num_samples_per_bin=5,           # 从3增到5，提高时间分辨率
    soft_nms_alpha=0.5,              # 从0.4增到0.5
    soft_nms_low_threshold=0.3,      # 从0.5降到0.3，更宽松
    soft_nms_high_threshold=0.8,     # 从0.9降到0.8，更宽松
    post_process_top_k=200)          # 从100增到200，保留更多候选

# 评估器设置 - 修改输出路径
val_evaluator = dict(
    dump_config=dict(
        out='../../../work_dirs/bmn_exp2_1_boundary_opt/results.json',
        output_format='json'))
test_evaluator = val_evaluator

# 训练配置 - 延长训练时间
max_epochs = 35                      # 增加训练轮数
train_cfg = dict(max_epochs=max_epochs, val_interval=2)

# 学习率调度 - 多阶段衰减
param_scheduler = [
    dict(
        type='MultiStepLR',
        begin=0,
        end=max_epochs,
        by_epoch=True,
        milestones=[15, 25, 30],     # 多阶段衰减
        gamma=0.5)
]

# 优化器配置 - 更保守的学习率
optim_wrapper = dict(
    optimizer=dict(lr=0.0008),       # 稍微提高学习率
    clip_grad=dict(max_norm=40, norm_type=2))

# 工作目录
work_dir = '../../../work_dirs/bmn_exp2_1_boundary_opt'

# 实验配置说明
config_info = {
    'experiment': '2.1 边界参数优化',
    'changes': [
        'boundary_ratio: 0.5 → 0.3 (提高边界敏感度)',
        'num_samples: 32 → 64 (增加采样密度)',
        'num_samples_per_bin: 3 → 5 (提高时间分辨率)',
        'soft_nms阈值调整为更宽松设置',
        'post_process_top_k: 100 → 200',
        '训练轮数增加到35轮',
        '多阶段学习率衰减'
    ],
    'expected_improvements': [
        '显著改善AR@1/5/10指标',
        '提高边界检测精度',
        '增强时间定位能力',
        '保持或提高AUC'
    ],
    'success_criteria': [
        'AR@1 > 0.02',
        'AR@5 > 0.08',
        'AR@10 > 0.15',
        'AR@100 > 0.04',
        'AUC > 2.0'
    ]
}
