#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实验1.2: 批次大小优化配置
目标: 通过梯度累积模拟更大的批次大小，改善训练稳定性
预期效果: 提高训练稳定性，改善梯度估计质量
"""

_base_ = [
    '../../../mmaction2/configs/_base_/default_runtime.py'
]

# 导入自定义变换模块
import fix_transforms  # noqa: F401

# 模型设置
model = dict(
    type='BMN',
    temporal_dim=100,
    boundary_ratio=0.5,
    num_samples=32,
    num_samples_per_bin=3,
    feat_dim=2048,
    soft_nms_alpha=0.4,
    soft_nms_low_threshold=0.5,
    soft_nms_high_threshold=0.9,
    post_process_top_k=100)

# 数据集设置
dataset_type = 'ActivityNetDataset'
data_root = '../../../data/MultiClassTAD/features_slowonly'
data_root_val = '../../../data/MultiClassTAD/features_slowonly'
ann_file_train = '../../../data/MultiClassTAD/multiclass_tad_train.json'
ann_file_val = '../../../data/MultiClassTAD/multiclass_tad_val.json'
ann_file_test = '../../../data/MultiClassTAD/multiclass_tad_val.json'

# 训练数据管道
train_pipeline = [
    dict(type='LoadLocalizationFeatureForBMN'),
    dict(type='GenerateLocalizationLabels'),
    dict(
        type='PackLocalizationInputs',
        keys=('gt_bbox', ),
        meta_keys=('video_name', ))
]

# 验证数据管道
val_pipeline = [
    dict(type='LoadLocalizationFeatureForBMN'),
    dict(type='GenerateLocalizationLabels'),
    dict(
        type='PackLocalizationInputs',
        keys=('gt_bbox', ),
        meta_keys=('video_name', 'duration_second', 'duration_frame',
                   'annotations', 'feature_frame'))
]

# 测试数据管道
test_pipeline = [
    dict(type='LoadLocalizationFeatureForBMN'),
    dict(
        type='PackLocalizationInputs',
        keys=('gt_bbox', ),
        meta_keys=('video_name', 'duration_second', 'duration_frame',
                   'annotations', 'feature_frame'))
]

# 数据加载器配置 - 使用梯度累积模拟大批次
train_dataloader = dict(
    batch_size=4,                    # 保持原始批次大小
    num_workers=4,
    persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=True),
    drop_last=True,
    dataset=dict(
        type=dataset_type,
        ann_file=ann_file_train,
        data_prefix=dict(video=data_root),
        pipeline=train_pipeline))

val_dataloader = dict(
    batch_size=1,
    num_workers=4,
    persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=False),
    dataset=dict(
        type=dataset_type,
        ann_file=ann_file_val,
        data_prefix=dict(video=data_root_val),
        pipeline=val_pipeline,
        test_mode=True))

test_dataloader = dict(
    batch_size=1,
    num_workers=4,
    persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=False),
    dataset=dict(
        type=dataset_type,
        ann_file=ann_file_test,
        data_prefix=dict(video=data_root_val),
        pipeline=test_pipeline,
        test_mode=True))

# 评估器设置
val_evaluator = dict(
    type='ANetMetric',
    metric_type='AR@AN',
    dump_config=dict(
        out='../../../work_dirs/bmn_exp1_2_batch_opt/results.json',
        output_format='json'))
test_evaluator = val_evaluator

# 训练配置
max_epochs = 25
train_cfg = dict(
    type='EpochBasedTrainLoop',
    max_epochs=max_epochs,
    val_begin=1,
    val_interval=2)

val_cfg = dict(type='ValLoop')
test_cfg = dict(type='TestLoop')

# 学习率调度
param_scheduler = [
    dict(
        type='MultiStepLR',
        begin=0,
        end=max_epochs,
        by_epoch=True,
        milestones=[15, 20],
        gamma=0.3)                   # 更强的衰减补偿小批次
]

# 优化器配置 - 使用梯度累积
optim_wrapper = dict(
    type='AmpOptimWrapper',          # 使用混合精度训练
    accumulative_counts=4,           # 梯度累积4次，等效batch_size=16
    optimizer=dict(
        type='AdamW',                # 使用AdamW优化器
        lr=0.001,                    # 保持原始学习率
        weight_decay=0.0001,
        betas=(0.9, 0.999)),
    clip_grad=dict(max_norm=40, norm_type=2))

# 工作目录
work_dir = '../../../work_dirs/bmn_exp1_2_batch_opt'

# 钩子配置
default_hooks = dict(
    checkpoint=dict(
        type='CheckpointHook',
        interval=5,
        save_best='auto',
        max_keep_ckpts=3),
    logger=dict(
        type='LoggerHook',
        ignore_last=False,
        interval=5),                 # 更频繁的日志记录
    param_scheduler=dict(type='ParamSchedulerHook'),
    runtime_info=dict(type='RuntimeInfoHook'),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    sync_buffers=dict(type='SyncBuffersHook'),
    timer=dict(type='IterTimerHook'))

# 环境配置
env_cfg = dict(
    cudnn_benchmark=False,
    dist_cfg=dict(backend='nccl'),
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=0))

# 日志配置
log_processor = dict(by_epoch=True, type='LogProcessor', window_size=20)
log_level = 'INFO'

# 可视化配置
vis_backends = [dict(type='LocalVisBackend')]
visualizer = dict(type='ActionVisualizer', vis_backends=vis_backends)

# 其他配置
default_scope = 'mmaction'
launcher = 'none'
load_from = None
resume = False
randomness = dict(deterministic=False, diff_rank_seed=False, seed=None)

# 实验配置说明
config_info = {
    'experiment': '1.2 批次大小优化',
    'changes': [
        '使用AmpOptimWrapper混合精度训练',
        '梯度累积4次，等效batch_size=16',
        '使用AdamW优化器替代Adam',
        '更强的学习率衰减策略',
        '更频繁的日志记录'
    ],
    'expected_improvements': [
        '提高训练稳定性',
        '改善梯度估计质量',
        '减少内存使用',
        '加快训练速度'
    ],
    'success_criteria': [
        '训练损失更稳定下降',
        'AR@100 > 0.035',
        'AUC > 2.1',
        '梯度范数更稳定'
    ]
}
