#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BMN自动化参数实验脚本
用于批量测试多个配置文件，自动收集和分析实验结果
完全参照train_bmn.py的结构和写法
"""

import os
import sys
import subprocess
import argparse
import json
import time
import glob
from pathlib import Path
from datetime import datetime
import re

class BMNExperimentRunner:
    """BMN实验运行器"""
    
    def __init__(self, configs_dir="../configs", results_dir="../../../experiment_results"):
        self.configs_dir = configs_dir
        self.results_dir = results_dir
        self.experiment_log = []
        self.start_time = datetime.now()
        
        # 创建结果目录
        os.makedirs(self.results_dir, exist_ok=True)
        
        # 初始化日志文件
        self.log_file = os.path.join(self.results_dir, f"experiment_log_{self.start_time.strftime('%Y%m%d_%H%M%S')}.txt")
        self.results_file = os.path.join(self.results_dir, f"experiment_results_{self.start_time.strftime('%Y%m%d_%H%M%S')}.json")
        
    def log_message(self, message, print_msg=True):
        """记录日志消息"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = f"[{timestamp}] {message}"
        
        if print_msg:
            print(log_entry)
            
        # 写入日志文件
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry + '\n')
    
    def check_environment(self):
        """检查训练环境 - 参照train_bmn.py"""
        self.log_message("🔍 检查训练环境...")
        
        # 检查CUDA
        try:
            import torch
            if torch.cuda.is_available():
                self.log_message(f"✅ CUDA可用: {torch.cuda.get_device_name(0)}")
                self.log_message(f"   GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
            else:
                self.log_message("⚠️  CUDA不可用，将使用CPU训练（速度较慢）")
        except ImportError:
            self.log_message("❌ PyTorch未安装")
            return False
        
        # 检查MMAction2
        try:
            import mmaction
            self.log_message(f"✅ MMAction2已安装: {mmaction.__version__}")
        except ImportError:
            self.log_message("❌ MMAction2未安装")
            return False
        
        return True

    def check_data_files(self):
        """检查数据文件 - 参照train_bmn.py"""
        self.log_message("\n📁 检查数据文件...")
        
        # 检查标注文件
        ann_files = [
            "../../../data/MultiClassTAD/multiclass_tad_train.json",
            "../../../data/MultiClassTAD/multiclass_tad_val.json"
        ]
        
        for ann_file in ann_files:
            if not os.path.exists(ann_file):
                self.log_message(f"❌ 标注文件不存在: {ann_file}")
                return False
            self.log_message(f"✅ 标注文件: {ann_file}")
        
        # 检查特征文件目录
        feature_dir = "../../../data/MultiClassTAD/features_slowonly"
        if not os.path.exists(feature_dir):
            self.log_message(f"❌ 特征目录不存在: {feature_dir}")
            return False

        # 统计特征文件数量
        csv_files = list(Path(feature_dir).rglob("*.csv"))
        self.log_message(f"✅ 特征目录: {feature_dir} ({len(csv_files)} 个CSV文件)")
        
        return True

    def get_experiment_configs(self):
        """获取实验配置文件列表"""
        self.log_message("\n📋 扫描实验配置文件...")
        
        # 查找所有实验配置文件
        config_pattern = os.path.join(self.configs_dir, "exp*.py")
        config_files = glob.glob(config_pattern)
        config_files.sort()  # 按文件名排序
        
        if not config_files:
            self.log_message(f"❌ 在 {self.configs_dir} 目录下未找到实验配置文件 (exp*.py)")
            return []
        
        self.log_message(f"✅ 找到 {len(config_files)} 个实验配置文件:")
        for i, config_file in enumerate(config_files, 1):
            config_name = os.path.basename(config_file)
            self.log_message(f"   {i}. {config_name}")
        
        return config_files

    def extract_experiment_info(self, config_file):
        """从配置文件中提取实验信息"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取实验描述
            description_match = re.search(r'"""([^"]*实验[^"]*?)"""', content, re.DOTALL)
            description = description_match.group(1).strip() if description_match else "无描述"
            
            # 提取关键参数（简单的正则匹配）
            params = {}
            
            # 提取一些关键参数
            param_patterns = {
                'boundary_ratio': r'boundary_ratio=([0-9.]+)',
                'num_samples': r'num_samples=([0-9]+)',
                'batch_size': r'batch_size=([0-9]+)',
                'lr': r'lr=([0-9.e-]+)',
                'max_epochs': r'max_epochs\s*=\s*([0-9]+)'
            }
            
            for param_name, pattern in param_patterns.items():
                match = re.search(pattern, content)
                if match:
                    params[param_name] = match.group(1)
            
            return {
                'description': description,
                'parameters': params
            }
        except Exception as e:
            self.log_message(f"⚠️  无法解析配置文件 {config_file}: {e}")
            return {
                'description': "解析失败",
                'parameters': {}
            }

    def run_single_experiment(self, config_file, gpu_ids="0"):
        """运行单个实验 - 参照train_bmn.py的start_training函数"""
        config_name = os.path.basename(config_file)
        exp_name = config_name.replace('.py', '')

        self.log_message(f"\n🚀 开始实验: {exp_name}")
        self.log_message(f"   配置文件: {config_file}")
        self.log_message(f"   GPU设备: {gpu_ids}")

        # 设置CUDA设备环境变量
        os.environ['CUDA_VISIBLE_DEVICES'] = gpu_ids

        # 获取MMAction2工具路径 - 参照train_bmn.py
        script_dir = os.path.dirname(os.path.abspath(__file__))
        mmaction_train_script = os.path.join(script_dir, "../../../mmaction2/tools/train.py")

        # 添加configs目录到Python路径，以便导入fix_transforms模块
        configs_dir = os.path.join(script_dir, "../configs")
        if configs_dir not in sys.path:
            sys.path.insert(0, configs_dir)

        # 确保训练脚本存在
        if not os.path.exists(mmaction_train_script):
            self.log_message(f"❌ MMAction2训练脚本不存在: {mmaction_train_script}")
            return None

        # 确保配置文件路径正确
        if not os.path.isabs(config_file):
            config_file = os.path.join(script_dir, config_file)

        if not os.path.exists(config_file):
            self.log_message(f"❌ 配置文件不存在: {config_file}")
            return None

        # 构建训练命令 - 参照train_bmn.py
        cmd = [
            "python", mmaction_train_script,
            config_file
        ]

        self.log_message(f"   执行命令: {' '.join(cmd)}")
        self.log_message("=" * 60)

        # 记录实验开始时间
        exp_start_time = time.time()

        # 执行训练
        try:
            # 设置环境变量，包括Python路径
            env = os.environ.copy()
            env['PYTHONPATH'] = configs_dir + ':' + env.get('PYTHONPATH', '')

            # 使用subprocess.Popen来实时捕获输出
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                cwd=script_dir,
                env=env
            )

            # 实时显示训练输出并记录
            output_lines = []
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    output_lines.append(output.strip())
                    # 只显示重要的训练信息
                    if any(keyword in output.lower() for keyword in ['epoch', 'loss', 'auc', 'ar@']):
                        self.log_message(f"   {output.strip()}")

            # 等待进程完成
            return_code = process.poll()

            if return_code == 0:
                exp_duration = time.time() - exp_start_time
                self.log_message(f"\n🎉 实验 {exp_name} 完成! 耗时: {exp_duration/60:.1f}分钟")

                # 解析训练结果
                results = self.parse_training_results(output_lines, config_file)
                results['experiment_name'] = exp_name
                results['config_file'] = config_file
                results['duration_minutes'] = exp_duration / 60
                results['status'] = 'success'

                return results
            else:
                self.log_message(f"\n❌ 实验 {exp_name} 失败! 返回码: {return_code}")
                return {
                    'experiment_name': exp_name,
                    'config_file': config_file,
                    'status': 'failed',
                    'error_code': return_code
                }

        except subprocess.CalledProcessError as e:
            self.log_message(f"\n❌ 实验 {exp_name} 执行失败: {e}")
            return {
                'experiment_name': exp_name,
                'config_file': config_file,
                'status': 'failed',
                'error': str(e)
            }
        except KeyboardInterrupt:
            self.log_message(f"\n⏹️  实验 {exp_name} 被用户中断")
            return {
                'experiment_name': exp_name,
                'config_file': config_file,
                'status': 'interrupted'
            }

    def parse_training_results(self, output_lines, config_file):
        """解析训练输出，提取关键指标"""
        results = {
            'final_metrics': {},
            'best_metrics': {},
            'training_history': []
        }

        # 解析训练输出中的指标
        for line in output_lines:
            line_lower = line.lower()

            # 解析AUC指标
            if 'auc' in line_lower and ':' in line:
                try:
                    auc_match = re.search(r'auc[:\s]*([0-9.]+)', line_lower)
                    if auc_match:
                        auc_value = float(auc_match.group(1))
                        results['final_metrics']['auc'] = auc_value
                except:
                    pass

            # 解析AR@指标
            for ar_type in ['ar@1', 'ar@5', 'ar@10']:
                if ar_type in line_lower:
                    try:
                        ar_match = re.search(f'{ar_type}[:\s]*([0-9.]+)', line_lower)
                        if ar_match:
                            ar_value = float(ar_match.group(1))
                            results['final_metrics'][ar_type] = ar_value
                    except:
                        pass

            # 解析损失值
            if 'loss' in line_lower and ':' in line:
                try:
                    loss_match = re.search(r'loss[:\s]*([0-9.]+)', line_lower)
                    if loss_match:
                        loss_value = float(loss_match.group(1))
                        results['final_metrics']['loss'] = loss_value
                except:
                    pass

        # 尝试从工作目录中读取详细结果
        try:
            # 从配置文件中提取工作目录
            with open(config_file, 'r') as f:
                config_content = f.read()

            work_dir_match = re.search(r"work_dir\s*=\s*['\"]([^'\"]+)['\"]", config_content)
            if work_dir_match:
                work_dir = work_dir_match.group(1)
                # 查找结果文件
                result_files = glob.glob(os.path.join(work_dir, "*.json"))
                for result_file in result_files:
                    if 'results' in os.path.basename(result_file):
                        with open(result_file, 'r') as f:
                            detailed_results = json.load(f)
                            results['detailed_results'] = detailed_results
                        break
        except:
            pass

        return results

    def run_all_experiments(self, gpu_ids="0", skip_failed=True):
        """运行所有实验"""
        self.log_message("\n" + "=" * 80)
        self.log_message("🎯 开始BMN自动化参数实验")
        self.log_message("=" * 80)

        # 环境检查
        if not self.check_environment():
            self.log_message("❌ 环境检查失败，请先安装必要的依赖")
            return False

        # 数据文件检查
        if not self.check_data_files():
            self.log_message("❌ 数据文件检查失败，请确保数据准备完整")
            return False

        # 获取实验配置
        config_files = self.get_experiment_configs()
        if not config_files:
            return False

        # 运行实验
        all_results = []
        successful_experiments = 0
        failed_experiments = 0

        for i, config_file in enumerate(config_files, 1):
            config_name = os.path.basename(config_file)

            self.log_message(f"\n{'='*60}")
            self.log_message(f"📊 实验进度: {i}/{len(config_files)} - {config_name}")
            self.log_message(f"{'='*60}")

            # 提取实验信息
            exp_info = self.extract_experiment_info(config_file)
            self.log_message(f"📝 实验描述: {exp_info['description'][:100]}...")
            if exp_info['parameters']:
                self.log_message(f"🔧 关键参数: {exp_info['parameters']}")

            # 运行实验
            result = self.run_single_experiment(config_file, gpu_ids)

            if result:
                result.update(exp_info)
                all_results.append(result)

                if result.get('status') == 'success':
                    successful_experiments += 1
                    self.log_message(f"✅ 实验 {config_name} 成功完成")

                    # 显示关键指标
                    metrics = result.get('final_metrics', {})
                    if metrics:
                        self.log_message("📈 关键指标:")
                        for metric, value in metrics.items():
                            self.log_message(f"   {metric}: {value}")
                else:
                    failed_experiments += 1
                    self.log_message(f"❌ 实验 {config_name} 失败")

                    if not skip_failed:
                        self.log_message("⏹️  由于实验失败，停止后续实验")
                        break
            else:
                failed_experiments += 1
                self.log_message(f"❌ 实验 {config_name} 无法运行")

                if not skip_failed:
                    break

            # 保存中间结果
            self.save_experiment_results(all_results)

        # 生成最终报告
        self.generate_final_report(all_results, successful_experiments, failed_experiments)

        return successful_experiments > 0

    def save_experiment_results(self, results):
        """保存实验结果到JSON文件"""
        try:
            with open(self.results_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'experiment_info': {
                        'start_time': self.start_time.isoformat(),
                        'total_experiments': len(results),
                        'successful_experiments': len([r for r in results if r.get('status') == 'success']),
                        'failed_experiments': len([r for r in results if r.get('status') != 'success'])
                    },
                    'results': results
                }, f, indent=2, ensure_ascii=False)

            self.log_message(f"💾 实验结果已保存到: {self.results_file}")
        except Exception as e:
            self.log_message(f"⚠️  保存实验结果失败: {e}")

    def generate_final_report(self, results, successful, failed):
        """生成最终实验报告"""
        total_time = (datetime.now() - self.start_time).total_seconds() / 60

        self.log_message("\n" + "=" * 80)
        self.log_message("📊 实验汇总报告")
        self.log_message("=" * 80)

        self.log_message(f"⏱️  总耗时: {total_time:.1f} 分钟")
        self.log_message(f"✅ 成功实验: {successful}")
        self.log_message(f"❌ 失败实验: {failed}")
        self.log_message(f"📁 详细日志: {self.log_file}")
        self.log_message(f"📄 结果文件: {self.results_file}")

        # 成功实验的指标对比
        successful_results = [r for r in results if r.get('status') == 'success']
        if successful_results:
            self.log_message("\n🏆 成功实验指标对比:")
            self.log_message("-" * 80)

            # 表头
            header = f"{'实验名称':<25} {'AUC':<8} {'AR@1':<8} {'AR@5':<8} {'AR@10':<8} {'耗时(分)':<8}"
            self.log_message(header)
            self.log_message("-" * 80)

            # 数据行
            for result in successful_results:
                exp_name = result.get('experiment_name', 'Unknown')[:24]
                metrics = result.get('final_metrics', {})
                auc = metrics.get('auc', 0.0)
                ar1 = metrics.get('ar@1', 0.0)
                ar5 = metrics.get('ar@5', 0.0)
                ar10 = metrics.get('ar@10', 0.0)
                duration = result.get('duration_minutes', 0.0)

                row = f"{exp_name:<25} {auc:<8.4f} {ar1:<8.4f} {ar5:<8.4f} {ar10:<8.4f} {duration:<8.1f}"
                self.log_message(row)

            # 找出最佳实验
            best_auc_exp = max(successful_results, key=lambda x: x.get('final_metrics', {}).get('auc', 0))
            best_ar1_exp = max(successful_results, key=lambda x: x.get('final_metrics', {}).get('ar@1', 0))

            self.log_message("\n🥇 最佳实验:")
            self.log_message(f"   最高AUC: {best_auc_exp.get('experiment_name')} (AUC: {best_auc_exp.get('final_metrics', {}).get('auc', 0):.4f})")
            self.log_message(f"   最高AR@1: {best_ar1_exp.get('experiment_name')} (AR@1: {best_ar1_exp.get('final_metrics', {}).get('ar@1', 0):.4f})")

        self.log_message("\n💡 后续建议:")
        if successful > 0:
            self.log_message("   1. 分析最佳实验的参数设置")
            self.log_message("   2. 基于最佳结果进行进一步的参数微调")
            self.log_message("   3. 考虑组合多个有效的优化策略")
        else:
            self.log_message("   1. 检查数据质量和预处理流程")
            self.log_message("   2. 降低学习率或调整优化器参数")
            self.log_message("   3. 增加训练轮数或调整模型结构")

        self.log_message("=" * 80)


def main():
    """主函数 - 参照train_bmn.py"""
    parser = argparse.ArgumentParser(description='BMN自动化参数实验脚本')
    parser.add_argument('--configs-dir', default='../configs',
                       help='配置文件目录路径')
    parser.add_argument('--results-dir', default='../../../experiment_results',
                       help='实验结果保存目录')
    parser.add_argument('--gpu-ids', default='0',
                       help='GPU设备ID，多个用逗号分隔')
    parser.add_argument('--skip-failed', action='store_true', default=True,
                       help='是否在实验失败后继续执行后续实验')
    parser.add_argument('--single-config', default=None,
                       help='仅运行指定的单个配置文件')

    args = parser.parse_args()

    print("=" * 80)
    print("🎯 BMN多类别时间动作定位自动化参数实验")
    print("=" * 80)

    # 创建实验运行器
    runner = BMNExperimentRunner(
        configs_dir=args.configs_dir,
        results_dir=args.results_dir
    )

    # 如果指定了单个配置文件
    if args.single_config:
        config_file = args.single_config
        if not os.path.exists(config_file):
            print(f"❌ 指定的配置文件不存在: {config_file}")
            return

        print(f"🔍 仅运行单个实验: {os.path.basename(config_file)}")

        # 环境检查
        if not runner.check_environment():
            print("❌ 环境检查失败，请先安装必要的依赖")
            return

        # 数据文件检查
        if not runner.check_data_files():
            print("❌ 数据文件检查失败，请确保数据准备完整")
            return

        # 运行单个实验
        result = runner.run_single_experiment(config_file, args.gpu_ids)

        if result and result.get('status') == 'success':
            print("\n✅ 实验成功完成")

            # 显示关键指标
            metrics = result.get('final_metrics', {})
            if metrics:
                print("📈 关键指标:")
                for metric, value in metrics.items():
                    print(f"   {metric}: {value}")
        else:
            print("\n❌ 实验失败")
    else:
        # 运行所有实验
        runner.run_all_experiments(args.gpu_ids, args.skip_failed)


if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️  程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()
