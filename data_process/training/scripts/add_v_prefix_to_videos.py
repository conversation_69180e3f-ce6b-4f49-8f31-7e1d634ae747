#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为raw_videos目录下的所有视频文件名添加v_前缀
"""

import os
import shutil
from pathlib import Path

def add_v_prefix_to_videos(video_dir):
    """
    为指定目录下的所有视频文件名添加v_前缀
    
    Args:
        video_dir (str): 视频文件目录路径
    """
    video_dir = Path(video_dir)
    
    if not video_dir.exists():
        print(f"错误: 目录不存在 {video_dir}")
        return
    
    print(f"正在处理目录: {video_dir}")
    
    # 常见的视频文件扩展名
    video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.webm', '.m4v', '.csv'}
    
    # 获取所有视频文件
    video_files = []
    for file_path in video_dir.iterdir():
        if file_path.is_file() and file_path.suffix.lower() in video_extensions:
            video_files.append(file_path)
    
    if not video_files:
        print("没有找到视频文件")
        return
    
    print(f"找到 {len(video_files)} 个视频文件")
    
    # 统计信息
    renamed_count = 0
    skipped_count = 0
    error_count = 0
    
    # 处理每个视频文件
    for file_path in video_files:
        original_name = file_path.name
        
        # 检查是否已经有v_前缀
        if original_name.startswith('v_'):
            print(f"跳过 (已有v_前缀): {original_name}")
            skipped_count += 1
            continue
        
        # 生成新的文件名
        new_name = f"v_{original_name}"
        new_path = file_path.parent / new_name
        
        # 检查新文件名是否已存在
        if new_path.exists():
            print(f"跳过 (目标文件已存在): {original_name} -> {new_name}")
            skipped_count += 1
            continue
        
        try:
            # 重命名文件
            file_path.rename(new_path)
            print(f"✓ 重命名: {original_name} -> {new_name}")
            renamed_count += 1
        except Exception as e:
            print(f"✗ 重命名失败: {original_name} -> {new_name}, 错误: {e}")
            error_count += 1
    
    # 输出统计结果
    print(f"\n=== 处理完成 ===")
    print(f"成功重命名: {renamed_count} 个文件")
    print(f"跳过文件: {skipped_count} 个文件")
    print(f"失败文件: {error_count} 个文件")
    print(f"总计处理: {len(video_files)} 个文件")

def backup_directory(source_dir, backup_dir):
    """
    备份目录（可选功能）
    
    Args:
        source_dir (str): 源目录
        backup_dir (str): 备份目录
    """
    source_dir = Path(source_dir)
    backup_dir = Path(backup_dir)
    
    if not source_dir.exists():
        print(f"错误: 源目录不存在 {source_dir}")
        return False
    
    try:
        if backup_dir.exists():
            shutil.rmtree(backup_dir)
        
        shutil.copytree(source_dir, backup_dir)
        print(f"✓ 已备份到: {backup_dir}")
        return True
    except Exception as e:
        print(f"✗ 备份失败: {e}")
        return False

def main():
    """主函数"""
    # 设置视频目录路径
    video_dir = "/home/<USER>/johnny_ws/mmaction2_ws/data/MultiClassTAD/features_slowonly"
    
    print("=== 视频文件名添加v_前缀工具 ===")
    print(f"目标目录: {video_dir}")
    
    # 询问是否需要备份
    backup_choice = input("是否需要备份原始文件？(y/n): ").lower().strip()
    
    if backup_choice in ['y', 'yes']:
        backup_dir = f"{video_dir}_backup"
        print(f"正在备份到: {backup_dir}")
        if not backup_directory(video_dir, backup_dir):
            print("备份失败，是否继续？(y/n): ", end="")
            continue_choice = input().lower().strip()
            if continue_choice not in ['y', 'yes']:
                print("操作已取消")
                return
    
    # 确认操作
    print(f"\n即将为 {video_dir} 目录下的所有视频文件名添加 'v_' 前缀")
    confirm = input("确认继续？(y/n): ").lower().strip()
    
    if confirm not in ['y', 'yes']:
        print("操作已取消")
        return
    
    # 执行重命名操作
    add_v_prefix_to_videos(video_dir)

if __name__ == "__main__":
    main()
