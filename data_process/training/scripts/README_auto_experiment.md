# BMN自动化参数实验脚本使用说明

## 概述

`auto_experiment_bmn.py` 是一个自动化参数实验脚本，用于批量测试多个BMN模型配置文件，自动收集和分析实验结果。该脚本完全参照 `train_bmn.py` 的结构和写法，确保训练进程能够正常运行。

## 功能特性

- ✅ 自动遍历并测试 `configs` 目录下的所有实验配置文件
- ✅ 实时显示训练进度和关键指标
- ✅ 自动收集和分析模型的关键指标（AUC、AR@1/5/10、损失值等）
- ✅ 记录详细的实验日志到文件
- ✅ 生成实验结果汇总报告
- ✅ 支持错误处理和实验中断恢复
- ✅ 支持单个实验和批量实验模式

## 使用方法

### 1. 基本用法（运行所有实验）

```bash
# 激活conda环境
conda activate mmaction2-tad

# 进入脚本目录
cd ~/johnny_ws/mmaction2_ws/data_process/training/scripts

# 运行所有实验配置
python auto_experiment_bmn.py
```

### 2. 指定GPU设备

```bash
# 使用GPU 0
python auto_experiment_bmn.py --gpu-ids 0

# 使用多个GPU（如果支持）
python auto_experiment_bmn.py --gpu-ids 0,1
```

### 3. 运行单个实验

```bash
# 仅运行指定的配置文件
python auto_experiment_bmn.py --single-config ../configs/exp1_1_data_augmentation.py
```

### 4. 自定义目录

```bash
# 指定配置文件目录和结果保存目录
python auto_experiment_bmn.py \
    --configs-dir ../configs \
    --results-dir ../../../experiment_results
```

### 5. 完整参数示例

```bash
python auto_experiment_bmn.py \
    --configs-dir ../configs \
    --results-dir ../../../experiment_results \
    --gpu-ids 0 \
    --skip-failed
```

## 命令行参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--configs-dir` | `../configs` | 配置文件目录路径 |
| `--results-dir` | `../../../experiment_results` | 实验结果保存目录 |
| `--gpu-ids` | `0` | GPU设备ID，多个用逗号分隔 |
| `--skip-failed` | `True` | 是否在实验失败后继续执行后续实验 |
| `--single-config` | `None` | 仅运行指定的单个配置文件 |

## 输出文件

脚本会在结果目录中生成以下文件：

1. **实验日志**: `experiment_log_YYYYMMDD_HHMMSS.txt`
   - 包含详细的实验执行日志
   - 记录每个实验的进度和关键指标

2. **结果文件**: `experiment_results_YYYYMMDD_HHMMSS.json`
   - JSON格式的结构化实验结果
   - 包含所有实验的参数和指标数据

## 实验结果分析

脚本会自动生成实验汇总报告，包括：

- 📊 实验成功/失败统计
- 🏆 各实验关键指标对比表
- 🥇 最佳实验识别（最高AUC、最高AR@1等）
- 💡 后续优化建议

### 示例输出

```
================================================================================
📊 实验汇总报告
================================================================================
⏱️  总耗时: 45.2 分钟
✅ 成功实验: 3
❌ 失败实验: 0
📁 详细日志: /path/to/experiment_log_20231201_143022.txt
📄 结果文件: /path/to/experiment_results_20231201_143022.json

🏆 成功实验指标对比:
--------------------------------------------------------------------------------
实验名称                   AUC      AR@1     AR@5     AR@10    耗时(分)
--------------------------------------------------------------------------------
exp1_1_data_augmentation  2.1234   0.0123   0.0456   0.0789   15.2    
exp1_2_batch_optimization 2.0987   0.0098   0.0432   0.0765   14.8    
exp2_1_boundary_optimizat 2.1567   0.0145   0.0478   0.0812   15.2    

🥇 最佳实验:
   最高AUC: exp2_1_boundary_optimization (AUC: 2.1567)
   最高AR@1: exp2_1_boundary_optimization (AR@1: 0.0145)
```

## 注意事项

1. **环境要求**: 确保已激活 `mmaction2-tad` conda环境
2. **数据准备**: 确保数据文件和特征文件已正确准备
3. **磁盘空间**: 每个实验会生成模型检查点，确保有足够磁盘空间
4. **GPU内存**: 根据GPU内存调整配置文件中的batch_size
5. **实验时间**: 每个实验可能需要15-30分钟，请合理安排时间

## 故障排除

1. **CUDA内存不足**: 减小配置文件中的batch_size
2. **配置文件错误**: 检查配置文件语法和路径
3. **数据文件缺失**: 确保所有数据文件路径正确
4. **权限问题**: 确保对工作目录有写权限

## 扩展使用

可以通过修改脚本来：
- 添加更多指标解析
- 自定义实验筛选条件
- 集成可视化功能
- 添加邮件通知功能
