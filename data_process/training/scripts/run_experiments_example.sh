#!/bin/bash
# -*- coding: utf-8 -*-
"""
BMN自动化实验脚本使用示例
演示如何使用auto_experiment_bmn.py进行参数优化实验
"""

echo "============================================================"
echo "🎯 BMN自动化参数实验示例"
echo "============================================================"

# 激活conda环境
echo "📦 激活conda环境: mmaction2-tad"
source ~/anaconda3/etc/profile.d/conda.sh
conda activate mmaction2-tad

# 检查环境
echo "🔍 检查Python环境..."
python --version
echo "📍 当前工作目录: $(pwd)"

echo ""
echo "============================================================"
echo "📋 可用的实验选项"
echo "============================================================"

echo "1️⃣  运行所有实验 (推荐)"
echo "   python auto_experiment_bmn.py"
echo ""

echo "2️⃣  运行单个实验"
echo "   python auto_experiment_bmn.py --single-config ../configs/exp1_1_data_augmentation.py"
echo "   python auto_experiment_bmn.py --single-config ../configs/exp1_2_batch_optimization.py"
echo "   python auto_experiment_bmn.py --single-config ../configs/exp2_1_boundary_optimization.py"
echo ""

echo "3️⃣  指定GPU设备"
echo "   python auto_experiment_bmn.py --gpu-ids 0"
echo ""

echo "4️⃣  自定义结果目录"
echo "   python auto_experiment_bmn.py --results-dir /path/to/custom/results"
echo ""

echo "5️⃣  查看帮助信息"
echo "   python auto_experiment_bmn.py --help"
echo ""

echo "============================================================"
echo "🚀 开始实验"
echo "============================================================"

# 询问用户选择
read -p "请选择要执行的操作 (1-5, 或按Enter运行所有实验): " choice

case $choice in
    1|"")
        echo "🎯 运行所有实验..."
        python auto_experiment_bmn.py
        ;;
    2)
        echo "📋 可用的配置文件:"
        ls -1 ../configs/exp*.py | nl
        read -p "请输入配置文件编号: " config_num
        config_file=$(ls -1 ../configs/exp*.py | sed -n "${config_num}p")
        if [ -n "$config_file" ]; then
            echo "🎯 运行单个实验: $config_file"
            python auto_experiment_bmn.py --single-config "$config_file"
        else
            echo "❌ 无效的配置文件编号"
        fi
        ;;
    3)
        read -p "请输入GPU设备ID (例如: 0 或 0,1): " gpu_ids
        echo "🎯 使用GPU $gpu_ids 运行所有实验..."
        python auto_experiment_bmn.py --gpu-ids "$gpu_ids"
        ;;
    4)
        read -p "请输入结果保存目录: " results_dir
        echo "🎯 结果将保存到: $results_dir"
        python auto_experiment_bmn.py --results-dir "$results_dir"
        ;;
    5)
        python auto_experiment_bmn.py --help
        ;;
    *)
        echo "❌ 无效选择，退出"
        exit 1
        ;;
esac

echo ""
echo "============================================================"
echo "📊 实验完成"
echo "============================================================"

# 显示结果文件
if [ -d "../../../experiment_results" ]; then
    echo "📁 实验结果文件:"
    ls -la ../../../experiment_results/ | grep -E "\.(txt|json)$" | tail -5
    
    echo ""
    echo "💡 后续操作建议:"
    echo "   1. 查看详细日志: cat ../../../experiment_results/experiment_log_*.txt"
    echo "   2. 分析结果数据: python -m json.tool ../../../experiment_results/experiment_results_*.json"
    echo "   3. 基于最佳结果进行进一步优化"
fi

echo ""
echo "🎉 实验脚本执行完成!"
