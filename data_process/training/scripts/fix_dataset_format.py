#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复BMN数据集格式问题
解决验证指标为0的问题
"""

import json
import os
import pandas as pd
import numpy as np
from pathlib import Path

def fix_annotation_format(input_file, output_file, features_dir):
    """
    修复标注文件格式，添加BMN所需的字段
    
    Args:
        input_file: 原始标注文件路径
        output_file: 修复后的标注文件路径
        features_dir: 特征文件目录
    """
    print(f"正在修复标注文件: {input_file}")
    
    # 加载原始标注
    with open(input_file, 'r') as f:
        original_data = json.load(f)
    
    fixed_data = {}
    
    for video_name, video_info in original_data.items():
        # 添加v_前缀以匹配BMN期望的格式
        fixed_video_name = f"v_{video_name}"
        
        # 检查对应的特征文件
        feature_file = os.path.join(features_dir, f"{video_name}.csv")
        if not os.path.exists(feature_file):
            print(f"警告: 特征文件不存在 {feature_file}")
            continue
            
        # 读取特征文件获取实际的特征帧数
        try:
            feature_data = pd.read_csv(feature_file)
            feature_frame = len(feature_data)  # 实际特征帧数
        except Exception as e:
            print(f"错误: 无法读取特征文件 {feature_file}: {e}")
            continue
        
        # 构建修复后的视频信息
        fixed_video_info = {
            "duration_second": video_info["duration_second"],
            "duration_frame": video_info["duration_frame"],
            "annotations": video_info["annotations"],
            "feature_frame": feature_frame,  # 添加特征帧数
            "fps": video_info["duration_frame"] / video_info["duration_second"],  # 计算fps
            "rfps": video_info["duration_frame"] / video_info["duration_second"]  # 计算rfps
        }
        
        fixed_data[fixed_video_name] = fixed_video_info
    
    # 保存修复后的标注文件
    with open(output_file, 'w') as f:
        json.dump(fixed_data, f, indent=2)
    
    print(f"修复完成，保存到: {output_file}")
    print(f"处理了 {len(fixed_data)} 个视频")

def validate_feature_dimensions(features_dir):
    """
    验证特征文件的维度
    """
    print(f"正在验证特征文件维度: {features_dir}")
    
    feature_files = list(Path(features_dir).glob("*.csv"))
    if not feature_files:
        print("错误: 没有找到特征文件")
        return
    
    # 检查第一个文件的维度
    sample_file = feature_files[0]
    try:
        data = pd.read_csv(sample_file)
        print(f"特征文件维度: {data.shape}")
        print(f"时间步数: {len(data)}")
        print(f"特征维度: {len(data.columns)}")
        
        # 检查是否需要转置
        if len(data.columns) == 100 and len(data) > 100:
            print("警告: 特征文件可能需要转置 (当前是 [feat_dim, temporal_dim])")
            print("BMN期望格式: [temporal_dim, feat_dim]")
        elif len(data.columns) > 100 and len(data) == 100:
            print("特征文件格式正确: [temporal_dim, feat_dim]")
        else:
            print(f"特征文件格式: [{len(data)} x {len(data.columns)}]")
            
    except Exception as e:
        print(f"错误: 无法读取特征文件 {sample_file}: {e}")

def main():
    """主函数"""
    # 设置路径
    base_dir = Path(__file__).parent.parent.parent.parent
    data_dir = base_dir / "data" / "MultiClassTAD"
    features_dir = data_dir / "features_slowonly"
    
    # 原始和修复后的标注文件路径
    train_input = data_dir / "multiclass_tad_train.json"
    train_output = data_dir / "multiclass_tad_train_fixed.json"
    val_input = data_dir / "multiclass_tad_val.json"
    val_output = data_dir / "multiclass_tad_val_fixed.json"
    
    print("=== BMN数据集格式修复工具 ===")
    
    # 验证特征文件维度
    validate_feature_dimensions(features_dir)
    
    # 修复训练集标注
    if train_input.exists():
        fix_annotation_format(train_input, train_output, features_dir)
    else:
        print(f"错误: 训练集标注文件不存在 {train_input}")
    
    # 修复验证集标注
    if val_input.exists():
        fix_annotation_format(val_input, val_output, features_dir)
    else:
        print(f"错误: 验证集标注文件不存在 {val_input}")
    
    print("\n=== 修复完成 ===")
    print("请使用修复后的标注文件重新训练模型")

if __name__ == "__main__":
    main()
