#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BMN数据加载调试脚本
用于验证数据格式和模型输入输出
"""

import sys
import os
import json
import numpy as np
import pandas as pd
from pathlib import Path

# 添加mmaction2路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent / "mmaction2"))

from mmengine.config import Config
from mmengine.registry import DATASETS, MODELS
import torch

def debug_dataset_loading(config_path):
    """调试数据集加载"""
    print("=== 调试数据集加载 ===")
    
    # 加载配置
    cfg = Config.fromfile(config_path)
    
    # 构建数据集
    print("构建训练数据集...")
    train_dataset = DATASETS.build(cfg.train_dataloader.dataset)
    print(f"训练集大小: {len(train_dataset)}")

    print("构建验证数据集...")
    val_dataset = DATASETS.build(cfg.val_dataloader.dataset)
    print(f"验证集大小: {len(val_dataset)}")
    
    # 检查第一个样本
    if len(train_dataset) > 0:
        print("\n=== 检查训练集第一个样本 ===")
        sample = train_dataset[0]
        print(f"样本键: {sample.keys()}")
        
        if 'inputs' in sample:
            inputs = sample['inputs']
            print(f"输入形状: {inputs.shape}")
            print(f"输入数据类型: {inputs.dtype}")
            print(f"输入数据范围: [{inputs.min():.4f}, {inputs.max():.4f}]")
        
        if 'data_samples' in sample:
            data_sample = sample['data_samples']
            print(f"数据样本类型: {type(data_sample)}")
            if hasattr(data_sample, 'metainfo'):
                print(f"元信息: {data_sample.metainfo}")
    
    # 检查验证集样本
    if len(val_dataset) > 0:
        print("\n=== 检查验证集第一个样本 ===")
        sample = val_dataset[0]
        print(f"样本键: {sample.keys()}")
        
        if 'data_samples' in sample:
            data_sample = sample['data_samples']
            if hasattr(data_sample, 'metainfo'):
                metainfo = data_sample.metainfo
                print(f"视频名称: {metainfo.get('video_name', 'N/A')}")
                print(f"持续时间: {metainfo.get('duration_second', 'N/A')}秒")
                print(f"标注数量: {len(metainfo.get('annotations', []))}")
                
                # 检查标注格式
                annotations = metainfo.get('annotations', [])
                if annotations:
                    print(f"第一个标注: {annotations[0]}")

def debug_model_forward(config_path):
    """调试模型前向传播"""
    print("\n=== 调试模型前向传播 ===")
    
    # 加载配置
    cfg = Config.fromfile(config_path)
    
    # 构建模型
    model = MODELS.build(cfg.model)
    model.eval()
    
    print(f"模型类型: {type(model)}")
    print(f"模型参数: {cfg.model}")
    
    # 创建模拟输入
    batch_size = 2
    feat_dim = cfg.model.feat_dim
    temporal_dim = cfg.model.temporal_dim
    
    # 模拟输入数据
    inputs = torch.randn(batch_size, temporal_dim, feat_dim)
    print(f"模拟输入形状: {inputs.shape}")
    
    # 模拟数据样本
    data_samples = []
    for i in range(batch_size):
        from mmaction.structures import ActionDataSample
        data_sample = ActionDataSample()
        data_sample.set_metainfo({
            'video_name': f'v_test_video_{i}',
            'duration_second': 100.0,
            'duration_frame': 2500,
            'annotations': [
                {'segment': [10.0, 20.0], 'label': 'test_action'}
            ],
            'feature_frame': temporal_dim
        })
        data_samples.append(data_sample)
    
    # 前向传播
    try:
        with torch.no_grad():
            outputs = model(inputs, data_samples, mode='predict')
        print(f"模型输出类型: {type(outputs)}")
        if isinstance(outputs, list) and len(outputs) > 0:
            print(f"输出数量: {len(outputs)}")
            print(f"第一个输出: {outputs[0]}")
    except Exception as e:
        print(f"前向传播错误: {e}")

def check_feature_files(features_dir):
    """检查特征文件格式"""
    print(f"\n=== 检查特征文件格式 ===")
    
    feature_files = list(Path(features_dir).glob("*.csv"))
    if not feature_files:
        print("错误: 没有找到特征文件")
        return
    
    print(f"找到 {len(feature_files)} 个特征文件")
    
    # 检查前几个文件
    for i, file_path in enumerate(feature_files[:3]):
        print(f"\n检查文件 {i+1}: {file_path.name}")
        try:
            data = pd.read_csv(file_path)
            print(f"  形状: {data.shape}")
            print(f"  数据类型: {data.dtypes.iloc[0]}")
            print(f"  数据范围: [{data.values.min():.4f}, {data.values.max():.4f}]")
            print(f"  是否包含NaN: {data.isnull().any().any()}")
            
            # 检查前几行和前几列
            print(f"  前3行前5列:")
            print(data.iloc[:3, :5])
            
        except Exception as e:
            print(f"  错误: {e}")

def check_annotation_files(ann_file):
    """检查标注文件格式"""
    print(f"\n=== 检查标注文件格式 ===")
    print(f"文件: {ann_file}")
    
    try:
        with open(ann_file, 'r') as f:
            data = json.load(f)
        
        print(f"视频数量: {len(data)}")
        
        # 检查前几个视频
        video_names = list(data.keys())[:3]
        for video_name in video_names:
            video_info = data[video_name]
            print(f"\n视频: {video_name}")
            print(f"  持续时间: {video_info.get('duration_second', 'N/A')}秒")
            print(f"  帧数: {video_info.get('duration_frame', 'N/A')}")
            print(f"  特征帧数: {video_info.get('feature_frame', 'N/A')}")
            print(f"  FPS: {video_info.get('fps', 'N/A')}")
            print(f"  标注数量: {len(video_info.get('annotations', []))}")
            
            # 检查标注格式
            annotations = video_info.get('annotations', [])
            if annotations:
                print(f"  第一个标注: {annotations[0]}")
                
    except Exception as e:
        print(f"错误: {e}")

def main():
    """主函数"""
    # 设置路径
    base_dir = Path(__file__).parent.parent.parent.parent
    config_path = base_dir / "data_process" / "training" / "configs" / "bmn_multiclass_tad_config.py"
    features_dir = base_dir / "data" / "MultiClassTAD" / "features_slowonly"
    train_ann = base_dir / "data" / "MultiClassTAD" / "multiclass_tad_train.json"
    val_ann = base_dir / "data" / "MultiClassTAD" / "multiclass_tad_val.json"
    
    print("=== BMN数据调试工具 ===")
    
    # 检查特征文件
    if features_dir.exists():
        check_feature_files(features_dir)
    else:
        print(f"错误: 特征目录不存在 {features_dir}")
    
    # 检查标注文件
    if train_ann.exists():
        check_annotation_files(train_ann)
    else:
        print(f"错误: 训练标注文件不存在 {train_ann}")
    
    if val_ann.exists():
        check_annotation_files(val_ann)
    else:
        print(f"错误: 验证标注文件不存在 {val_ann}")
    
    # 调试数据集加载
    if config_path.exists():
        try:
            debug_dataset_loading(config_path)
            debug_model_forward(config_path)
        except Exception as e:
            print(f"调试过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
    else:
        print(f"错误: 配置文件不存在 {config_path}")

if __name__ == "__main__":
    main()
