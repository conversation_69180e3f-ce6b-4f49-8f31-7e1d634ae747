#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键完成视频特征提取的完整流水线
从分割视频到最终CSV格式特征文件的完整流程
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def run_command(cmd, description, cwd=None):
    """
    运行命令并处理结果

    Args:
        cmd (list): 命令列表
        description (str): 命令描述
        cwd (str): 工作目录

    Returns:
        bool: 是否成功
    """
    print(f"\n{'='*60}")
    print(f"🚀 {description}")
    print(f"{'='*60}")
    print(f"命令: {' '.join(cmd)}")

    try:
        result = subprocess.run(cmd, cwd=cwd, capture_output=True, text=True)

        if result.returncode == 0:
            print(f"✅ {description} 完成!")
            if result.stdout.strip():
                print("输出:")
                print(result.stdout)
            return True
        else:
            print(f"❌ {description} 失败!")
            print("错误:")
            print(result.stderr)
            return False

    except Exception as e:
        print(f"❌ 执行 {description} 时出错: {e}")
        return False

def check_prerequisites():
    """检查前置条件"""
    print("🔍 检查前置条件...")

    # 检查必要的目录和文件
    required_paths = [
        '../../../data/segmented_videos',  # 分割视频目录
        '../../../data/raw_videos',  # 原始视频目录
        '../../preprocessing/data_organization/extract_video_info.py',  # 视频信息提取脚本
        '../../preprocessing/data_organization/create_video_list.py',  # 视频列表生成脚本
        '../../preprocessing/feature_extraction/extract_slowonly_features.py',  # 特征提取脚本
        '../../preprocessing/feature_extraction/postprocess_features.py'  # 特征后处理脚本
    ]

    missing = []
    for path in required_paths:
        if not os.path.exists(path):
            missing.append(path)

    if missing:
        print("❌ 缺少必要的文件或目录:")
        for path in missing:
            print(f"   - {path}")
        return False

    # 检查分割视频目录是否有视频文件
    segmented_videos_dir = Path('../../../data/segmented_videos')
    video_files = list(segmented_videos_dir.rglob('*.mp4'))
    if not video_files:
        print("❌ 分割视频目录中没有找到MP4文件")
        print(f"   请确保 {segmented_videos_dir} 目录下有分割好的视频文件")
        return False

    print(f"✅ 前置条件检查通过，找到 {len(video_files)} 个视频文件")
    return True

def main():
    parser = argparse.ArgumentParser(description='一键完成视频特征提取的完整流水线')
    parser.add_argument('--skip-video-info', action='store_true',
                       help='跳过视频信息提取步骤（如果已经提取过）')
    parser.add_argument('--skip-feature-extraction', action='store_true',
                       help='跳过特征提取步骤（如果已经提取过）')
    parser.add_argument('--skip-postprocess', action='store_true',
                       help='跳过特征后处理步骤')
    parser.add_argument('--input-dir',
                       default='/home/<USER>/johnny_ws/mmaction2_ws/data/segmented_videos',
                       help='分割视频文件目录路径')
    parser.add_argument('--output-dir',
                       default='/home/<USER>/johnny_ws/mmaction2_ws/data/MultiClassTAD',
                       help='输出数据集目录路径')
    parser.add_argument('--non-interactive', action='store_true',
                       help='非交互模式，使用默认参数（已弃用，现在默认为非交互模式）')
    parser.add_argument('--interactive', action='store_true',
                       help='交互模式，允许用户选择参数')

    args = parser.parse_args()

    print("🎯 一键完成视频特征提取的完整流水线")
    print("=" * 60)
    print(f"📁 输入目录: {args.input_dir}")
    print(f"📁 输出目录: {args.output_dir}")
    print("=" * 60)

    # 检查前置条件
    if not check_prerequisites():
        print("❌ 前置条件检查失败，请先准备必要的文件和目录")
        return

    # 步骤1: 提取视频信息
    if not args.skip_video_info:
        print("\n📝 步骤1: 提取视频信息")
        cmd = ['python', '../../preprocessing/data_organization/extract_video_info.py']
        # 默认使用非交互模式，除非用户明确要求交互模式
        if not hasattr(args, 'interactive') or not args.interactive:
            cmd.extend(['--non-interactive', '--input-dir', args.input_dir, '--output-dir', args.output_dir])
        else:
            cmd.extend(['--input-dir', args.input_dir, '--output-dir', args.output_dir])

        if not run_command(cmd, "提取视频信息"):
            print("❌ 视频信息提取失败")
            return
    else:
        print("\n⏭️  跳过视频信息提取步骤")

    # 步骤2: 生成视频列表文件
    print("\n📝 步骤2: 生成视频列表文件")
    if not run_command(['python', '../../preprocessing/data_organization/create_video_list.py'], "生成视频列表文件"):
        print("❌ 视频列表文件生成失败")
        return

    # 步骤3: 提取SlowOnly特征
    if not args.skip_feature_extraction:
        print("\n🔧 步骤3: 提取SlowOnly特征")
        if not run_command(['python', '../../preprocessing/feature_extraction/extract_slowonly_features.py'], "提取SlowOnly特征"):
            print("⚠️  特征提取失败，可能需要手动处理")
            print("   请检查MMAction2环境和预训练模型下载")
            return
    else:
        print("\n⏭️  跳过特征提取步骤")

    # 步骤4: 特征后处理
    if not args.skip_postprocess:
        print("\n🔄 步骤4: 后处理特征为CSV格式")
        if not run_command(['python', '../../preprocessing/feature_extraction/postprocess_features.py'], "后处理特征"):
            return
    else:
        print("\n⏭️  跳过特征后处理步骤")

    # 完成
    print("\n" + "=" * 60)
    print("🎉 视频特征提取流水线完成!")
    print("=" * 60)
    print("\n📋 生成的文件:")
    print(f"   📄 训练标注: {args.output_dir}/multiclass_tad_train.json")
    print(f"   📄 验证标注: {args.output_dir}/multiclass_tad_val.json")
    print(f"   📄 完整数据集: {args.output_dir}/multiclass_tad_full.json")
    print(f"   📁 原始特征: {args.output_dir}/features_slowonly_raw/")
    print(f"   📁 CSV特征: {args.output_dir}/features_slowonly_reconstructed/")

    print("\n🚀 下一步:")
    print("   1. 检查生成的特征文件格式是否正确")
    print("   2. 使用生成的数据集进行BMN模型训练")
    print("   3. 或者进行其他时间动作定位任务")

if __name__ == '__main__':
    main()
