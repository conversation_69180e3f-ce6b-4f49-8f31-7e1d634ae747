[2025-07-31 13:08:17] 
================================================================================
[2025-07-31 13:08:17] 🎯 开始BMN自动化参数实验
[2025-07-31 13:08:17] ================================================================================
[2025-07-31 13:08:17] 🔍 检查训练环境...
[2025-07-31 13:08:18] ✅ CUDA可用: NVIDIA RTX A5000
[2025-07-31 13:08:18]    GPU内存: 23.6GB
[2025-07-31 13:08:18] ✅ MMAction2已安装: 1.2.0
[2025-07-31 13:08:18] 
📁 检查数据文件...
[2025-07-31 13:08:18] ✅ 标注文件: ../../../data/MultiClassTAD/multiclass_tad_train.json
[2025-07-31 13:08:18] ✅ 标注文件: ../../../data/MultiClassTAD/multiclass_tad_val.json
[2025-07-31 13:08:18] ✅ 特征目录: ../../../data/MultiClassTAD/features_slowonly (10 个CSV文件)
[2025-07-31 13:08:18] 
📋 扫描实验配置文件...
[2025-07-31 13:08:18] ✅ 找到 3 个实验配置文件:
[2025-07-31 13:08:18]    1. exp1_1_data_augmentation.py
[2025-07-31 13:08:18]    2. exp1_2_batch_optimization.py
[2025-07-31 13:08:18]    3. exp2_1_boundary_optimization.py
[2025-07-31 13:08:18] 
============================================================
[2025-07-31 13:08:18] 📊 实验进度: 1/3 - exp1_1_data_augmentation.py
[2025-07-31 13:08:18] ============================================================
[2025-07-31 13:08:18] 📝 实验描述: 实验1.1: 数据增强优化配置
目标: 通过时间域数据增强解决数据集过小的问题
预期效果: 提高模型泛化能力，改善AR@1/5/10指标...
[2025-07-31 13:08:18] 🔧 关键参数: {'boundary_ratio': '0.5', 'num_samples': '32', 'batch_size': '8', 'lr': '0.0005', 'max_epochs': '30'}
[2025-07-31 13:08:18] 
🚀 开始实验: exp1_1_data_augmentation
[2025-07-31 13:08:18]    配置文件: ../configs/exp1_1_data_augmentation.py
[2025-07-31 13:08:18]    GPU设备: 0
[2025-07-31 13:08:18]    执行命令: python /home/<USER>/johnny_ws/mmaction2_ws/data_process/training/scripts/../../../mmaction2/tools/train.py /home/<USER>/johnny_ws/mmaction2_ws/data_process/training/scripts/../configs/exp1_1_data_augmentation.py
[2025-07-31 13:08:18] ============================================================
[2025-07-31 13:08:22]    '改善AR@1/5/10指标',
[2025-07-31 13:08:22]    'AR@1 > 0.01',
[2025-07-31 13:08:22]    'AR@5 > 0.05',
[2025-07-31 13:08:22]    'AR@10 > 0.10',
[2025-07-31 13:08:22]    'AUC保持在2.0以上',
[2025-07-31 13:08:22]    log_processor = dict(by_epoch=True, type='LogProcessor', window_size=20)
[2025-07-31 13:08:22]    max_epochs = 30
[2025-07-31 13:08:22]    by_epoch=True,
[2025-07-31 13:08:22]    metric_type='AR@AN',
[2025-07-31 13:08:22]    max_epochs=30, type='EpochBasedTrainLoop', val_begin=1, val_interval=2)
[2025-07-31 13:08:22]    metric_type='AR@AN',
[2025-07-31 13:08:24]    before_train_epoch:
[2025-07-31 13:08:24]    after_train_epoch:
[2025-07-31 13:08:24]    before_val_epoch:
[2025-07-31 13:08:24]    after_val_epoch:
[2025-07-31 13:08:24]    before_test_epoch:
[2025-07-31 13:08:24]    after_test_epoch:
[2025-07-31 13:08:27]    07/31 13:08:27 - mmengine - INFO - Epoch(train)  [1][1/1]  lr: 5.0000e-04  eta: 0:00:45  time: 1.5539  data_time: 0.5871  memory: 8060  grad_norm: 0.3410  loss: 2.1493
[2025-07-31 13:08:27]    07/31 13:08:27 - mmengine - INFO - Epoch(train)  [2][1/1]  lr: 5.0000e-04  eta: 0:00:31  time: 1.1380  data_time: 0.3640  memory: 8110  grad_norm: 0.3437  loss: 2.1437
[2025-07-31 13:08:28]    07/31 13:08:28 - mmengine - INFO - Epoch(val) [2][2/2]    auc: 1.3222  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0254  data_time: 0.0599  time: 0.3173
[2025-07-31 13:08:28]    07/31 13:08:28 - mmengine - INFO - The best checkpoint with 1.3222 auc at 2 epoch is saved to best_auc_epoch_2.pth.
[2025-07-31 13:08:30]    07/31 13:08:30 - mmengine - INFO - Epoch(train)  [3][1/1]  lr: 5.0000e-04  eta: 0:00:31  time: 1.1829  data_time: 0.4719  memory: 8110  grad_norm: 0.3724  loss: 2.1275
[2025-07-31 13:08:30]    07/31 13:08:30 - mmengine - INFO - Epoch(train)  [4][1/1]  lr: 5.0000e-04  eta: 0:00:27  time: 1.0663  data_time: 0.3886  memory: 8110  grad_norm: 0.4851  loss: 2.1029
[2025-07-31 13:08:31]    07/31 13:08:31 - mmengine - INFO - Epoch(val) [4][2/2]    auc: 1.7659  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0270  data_time: 0.0100  time: 0.2638
[2025-07-31 13:08:31]    07/31 13:08:31 - mmengine - INFO - The previous best checkpoint /home/<USER>/johnny_ws/mmaction2_ws/work_dirs/bmn_exp1_1_data_aug/best_auc_epoch_2.pth is removed
[2025-07-31 13:08:32]    07/31 13:08:32 - mmengine - INFO - The best checkpoint with 1.7659 auc at 4 epoch is saved to best_auc_epoch_4.pth.
[2025-07-31 13:08:33]    07/31 13:08:33 - mmengine - INFO - Epoch(train)  [5][1/1]  lr: 5.0000e-04  eta: 0:00:26  time: 1.0698  data_time: 0.4101  memory: 8110  grad_norm: 0.6671  loss: 2.0615
[2025-07-31 13:08:33]    07/31 13:08:33 - mmengine - INFO - Saving checkpoint at 5 epochs
[2025-07-31 13:08:35]    07/31 13:08:35 - mmengine - INFO - Epoch(train)  [6][1/1]  lr: 5.0000e-04  eta: 0:00:24  time: 1.0413  data_time: 0.3948  memory: 8110  grad_norm: 1.5729  loss: 2.0229
[2025-07-31 13:08:35]    07/31 13:08:35 - mmengine - INFO - Epoch(val) [6][2/2]    auc: 1.8873  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0286  data_time: 0.0106  time: 0.2635
[2025-07-31 13:08:35]    07/31 13:08:35 - mmengine - INFO - The previous best checkpoint /home/<USER>/johnny_ws/mmaction2_ws/work_dirs/bmn_exp1_1_data_aug/best_auc_epoch_4.pth is removed
[2025-07-31 13:08:36]    07/31 13:08:36 - mmengine - INFO - The best checkpoint with 1.8873 auc at 6 epoch is saved to best_auc_epoch_6.pth.
[2025-07-31 13:08:37]    07/31 13:08:37 - mmengine - INFO - Epoch(train)  [7][1/1]  lr: 5.0000e-04  eta: 0:00:23  time: 1.0231  data_time: 0.3860  memory: 8110  grad_norm: 1.9570  loss: 1.9863
[2025-07-31 13:08:38]    07/31 13:08:38 - mmengine - INFO - Epoch(train)  [8][1/1]  lr: 5.0000e-04  eta: 0:00:22  time: 1.0307  data_time: 0.4009  memory: 8110  grad_norm: 2.5154  loss: 1.9575
[2025-07-31 13:08:39]    07/31 13:08:39 - mmengine - INFO - Epoch(val) [8][2/2]    auc: 1.9000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0286  data_time: 0.0088  time: 0.2645
[2025-07-31 13:08:39]    07/31 13:08:39 - mmengine - INFO - The previous best checkpoint /home/<USER>/johnny_ws/mmaction2_ws/work_dirs/bmn_exp1_1_data_aug/best_auc_epoch_6.pth is removed
[2025-07-31 13:08:39]    07/31 13:08:39 - mmengine - INFO - The best checkpoint with 1.9000 auc at 8 epoch is saved to best_auc_epoch_8.pth.
[2025-07-31 13:08:41]    07/31 13:08:41 - mmengine - INFO - Epoch(train)  [9][1/1]  lr: 5.0000e-04  eta: 0:00:21  time: 1.0173  data_time: 0.3929  memory: 8110  grad_norm: 2.5183  loss: 1.9301
[2025-07-31 13:08:42]    07/31 13:08:42 - mmengine - INFO - Epoch(train) [10][1/1]  lr: 5.0000e-04  eta: 0:00:20  time: 1.0080  data_time: 0.3869  memory: 8110  grad_norm: 2.7963  loss: 1.9097
[2025-07-31 13:08:42]    07/31 13:08:42 - mmengine - INFO - Saving checkpoint at 10 epochs
[2025-07-31 13:08:43]    07/31 13:08:43 - mmengine - INFO - Epoch(val) [10][2/2]    auc: 1.7675  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0270  data_time: 0.0094  time: 0.2634
[2025-07-31 13:08:44]    07/31 13:08:44 - mmengine - INFO - Epoch(train) [11][1/1]  lr: 5.0000e-04  eta: 0:00:19  time: 1.0150  data_time: 0.3970  memory: 8110  grad_norm: 2.6161  loss: 1.8896
[2025-07-31 13:08:45]    07/31 13:08:45 - mmengine - INFO - Epoch(train) [12][1/1]  lr: 5.0000e-04  eta: 0:00:18  time: 1.0217  data_time: 0.4051  memory: 8110  grad_norm: 2.5227  loss: 1.8738
[2025-07-31 13:08:46]    07/31 13:08:46 - mmengine - INFO - Epoch(val) [12][2/2]    auc: 1.6135  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0270  data_time: 0.0096  time: 0.2615
[2025-07-31 13:08:47]    07/31 13:08:47 - mmengine - INFO - Epoch(train) [13][1/1]  lr: 5.0000e-04  eta: 0:00:17  time: 1.0259  data_time: 0.4117  memory: 8110  grad_norm: 2.4496  loss: 1.8573
[2025-07-31 13:08:48]    07/31 13:08:48 - mmengine - INFO - Epoch(train) [14][1/1]  lr: 5.0000e-04  eta: 0:00:16  time: 1.0179  data_time: 0.4056  memory: 8110  grad_norm: 2.3598  loss: 1.8415
[2025-07-31 13:08:48]    07/31 13:08:48 - mmengine - INFO - Epoch(val) [14][2/2]    auc: 0.5944  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0079  data_time: 0.0090  time: 0.2184
[2025-07-31 13:08:49]    07/31 13:08:49 - mmengine - INFO - Epoch(train) [15][1/1]  lr: 5.0000e-04  eta: 0:00:15  time: 1.0110  data_time: 0.4002  memory: 8110  grad_norm: 2.3471  loss: 1.8250
[2025-07-31 13:08:49]    07/31 13:08:49 - mmengine - INFO - Saving checkpoint at 15 epochs
[2025-07-31 13:08:51]    07/31 13:08:51 - mmengine - INFO - Epoch(train) [16][1/1]  lr: 5.0000e-04  eta: 0:00:14  time: 1.0152  data_time: 0.4056  memory: 8110  grad_norm: 2.3254  loss: 1.8085
[2025-07-31 13:08:51]    07/31 13:08:51 - mmengine - INFO - Epoch(val) [16][2/2]    auc: 0.2683  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0032  data_time: 0.0099  time: 0.1716
[2025-07-31 13:08:52]    07/31 13:08:52 - mmengine - INFO - Epoch(train) [17][1/1]  lr: 5.0000e-04  eta: 0:00:13  time: 1.0102  data_time: 0.4015  memory: 8110  grad_norm: 2.2958  loss: 1.7923
[2025-07-31 13:08:53]    07/31 13:08:53 - mmengine - INFO - Epoch(train) [18][1/1]  lr: 5.0000e-04  eta: 0:00:12  time: 1.0144  data_time: 0.4070  memory: 8110  grad_norm: 2.3303  loss: 1.7757
[2025-07-31 13:08:53]    07/31 13:08:53 - mmengine - INFO - Epoch(val) [18][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0084  time: 0.1199
[2025-07-31 13:08:54]    07/31 13:08:54 - mmengine - INFO - Epoch(train) [19][1/1]  lr: 5.0000e-04  eta: 0:00:10  time: 0.9994  data_time: 0.3928  memory: 8110  grad_norm: 2.4433  loss: 1.7576
[2025-07-31 13:08:55]    07/31 13:08:55 - mmengine - INFO - Epoch(train) [20][1/1]  lr: 5.0000e-04  eta: 0:00:10  time: 1.0051  data_time: 0.3986  memory: 8110  grad_norm: 2.5069  loss: 1.7407
[2025-07-31 13:08:55]    07/31 13:08:55 - mmengine - INFO - Saving checkpoint at 20 epochs
[2025-07-31 13:08:57]    07/31 13:08:57 - mmengine - INFO - Epoch(val) [20][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0109  time: 0.0865
[2025-07-31 13:08:58]    07/31 13:08:58 - mmengine - INFO - Epoch(train) [21][1/1]  lr: 2.5000e-04  eta: 0:00:09  time: 0.9825  data_time: 0.3944  memory: 8110  grad_norm: 2.5251  loss: 1.7024
[2025-07-31 13:09:00]    07/31 13:09:00 - mmengine - INFO - Epoch(train) [22][1/1]  lr: 2.5000e-04  eta: 0:00:08  time: 1.0099  data_time: 0.4215  memory: 8110  grad_norm: 2.6311  loss: 1.6615
[2025-07-31 13:09:00]    07/31 13:09:00 - mmengine - INFO - Epoch(val) [22][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0082  time: 0.0635
[2025-07-31 13:09:01]    07/31 13:09:01 - mmengine - INFO - Epoch(train) [23][1/1]  lr: 2.5000e-04  eta: 0:00:07  time: 0.9822  data_time: 0.3939  memory: 8110  grad_norm: 2.6475  loss: 1.6225
[2025-07-31 13:09:02]    07/31 13:09:02 - mmengine - INFO - Epoch(train) [24][1/1]  lr: 2.5000e-04  eta: 0:00:06  time: 1.0010  data_time: 0.4120  memory: 8110  grad_norm: 2.6762  loss: 1.5863
[2025-07-31 13:09:02]    07/31 13:09:02 - mmengine - INFO - Epoch(val) [24][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0085  time: 0.0452
[2025-07-31 13:09:03]    07/31 13:09:03 - mmengine - INFO - Epoch(train) [25][1/1]  lr: 2.5000e-04  eta: 0:00:05  time: 1.0096  data_time: 0.4211  memory: 8110  grad_norm: 2.6536  loss: 1.5565
[2025-07-31 13:09:03]    07/31 13:09:03 - mmengine - INFO - Saving checkpoint at 25 epochs
[2025-07-31 13:09:06]    07/31 13:09:06 - mmengine - INFO - Epoch(train) [26][1/1]  lr: 1.2500e-04  eta: 0:00:04  time: 1.0104  data_time: 0.4213  memory: 8110  grad_norm: 2.4253  loss: 1.5294
[2025-07-31 13:09:06]    07/31 13:09:06 - mmengine - INFO - Epoch(val) [26][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0088  time: 0.0540
[2025-07-31 13:09:07]    07/31 13:09:07 - mmengine - INFO - Epoch(train) [27][1/1]  lr: 1.2500e-04  eta: 0:00:03  time: 1.0103  data_time: 0.4207  memory: 8110  grad_norm: 2.2881  loss: 1.5045
[2025-07-31 13:09:08]    07/31 13:09:08 - mmengine - INFO - Epoch(train) [28][1/1]  lr: 1.2500e-04  eta: 0:00:02  time: 1.0102  data_time: 0.4205  memory: 8110  grad_norm: 2.0204  loss: 1.4797
[2025-07-31 13:09:09]    07/31 13:09:09 - mmengine - INFO - Epoch(val) [28][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0085  time: 0.0560
[2025-07-31 13:09:09]    07/31 13:09:09 - mmengine - INFO - Epoch(train) [29][1/1]  lr: 1.2500e-04  eta: 0:00:01  time: 1.0019  data_time: 0.4118  memory: 8110  grad_norm: 1.9246  loss: 1.4565
[2025-07-31 13:09:10]    07/31 13:09:10 - mmengine - INFO - Epoch(train) [30][1/1]  lr: 1.2500e-04  eta: 0:00:00  time: 1.0011  data_time: 0.4115  memory: 8110  grad_norm: 1.7047  loss: 1.4319
[2025-07-31 13:09:10]    07/31 13:09:10 - mmengine - INFO - Saving checkpoint at 30 epochs
[2025-07-31 13:09:12]    07/31 13:09:12 - mmengine - INFO - Epoch(val) [30][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0100  time: 0.0498
[2025-07-31 13:09:13] 
🎉 实验 exp1_1_data_augmentation 完成! 耗时: 0.9分钟
[2025-07-31 13:09:13] ✅ 实验 exp1_1_data_augmentation.py 成功完成
[2025-07-31 13:09:13] 📈 关键指标:
[2025-07-31 13:09:13]    ar@1: 0.0
[2025-07-31 13:09:13]    loss: 1.4319
[2025-07-31 13:09:13]    auc: 0.0
[2025-07-31 13:09:13]    ar@5: 0.0
[2025-07-31 13:09:13]    ar@10: 0.0
[2025-07-31 13:09:13] 💾 实验结果已保存到: ../../../experiment_results/experiment_results_20250731_130817.json
[2025-07-31 13:09:13] 
============================================================
[2025-07-31 13:09:13] 📊 实验进度: 2/3 - exp1_2_batch_optimization.py
[2025-07-31 13:09:13] ============================================================
[2025-07-31 13:09:13] 📝 实验描述: 实验1.2: 批次大小优化配置
目标: 通过梯度累积模拟更大的批次大小，改善训练稳定性
预期效果: 提高训练稳定性，改善梯度估计质量...
[2025-07-31 13:09:13] 🔧 关键参数: {'boundary_ratio': '0.5', 'num_samples': '32', 'batch_size': '4', 'lr': '0.001', 'max_epochs': '25'}
[2025-07-31 13:09:13] 
🚀 开始实验: exp1_2_batch_optimization
[2025-07-31 13:09:13]    配置文件: ../configs/exp1_2_batch_optimization.py
[2025-07-31 13:09:13]    GPU设备: 0
[2025-07-31 13:09:13]    执行命令: python /home/<USER>/johnny_ws/mmaction2_ws/data_process/training/scripts/../../../mmaction2/tools/train.py /home/<USER>/johnny_ws/mmaction2_ws/data_process/training/scripts/../configs/exp1_2_batch_optimization.py
[2025-07-31 13:09:13] ============================================================
[2025-07-31 13:09:17]    'AR@100 > 0.035',
[2025-07-31 13:09:17]    'AUC > 2.1',
[2025-07-31 13:09:17]    log_processor = dict(by_epoch=True, type='LogProcessor', window_size=20)
[2025-07-31 13:09:17]    max_epochs = 25
[2025-07-31 13:09:17]    by_epoch=True,
[2025-07-31 13:09:17]    metric_type='AR@AN',
[2025-07-31 13:09:17]    max_epochs=25, type='EpochBasedTrainLoop', val_begin=1, val_interval=2)
[2025-07-31 13:09:17]    metric_type='AR@AN',
[2025-07-31 13:09:20]    before_train_epoch:
[2025-07-31 13:09:20]    after_train_epoch:
[2025-07-31 13:09:20]    before_val_epoch:
[2025-07-31 13:09:20]    after_val_epoch:
[2025-07-31 13:09:20]    before_test_epoch:
[2025-07-31 13:09:20]    after_test_epoch:
[2025-07-31 13:09:20]    self.loss_scaler = scaler_type()
[2025-07-31 13:09:21]    07/31 13:09:21 - mmengine - INFO - Epoch(train)  [1][2/2]  lr: 1.0000e-03  eta: 0:00:20  time: 0.4209  data_time: 0.0938  memory: 2467  loss: 2.1454
[2025-07-31 13:09:22]    07/31 13:09:22 - mmengine - INFO - Epoch(train)  [2][2/2]  lr: 1.0000e-03  eta: 0:00:13  time: 0.2994  data_time: 0.0679  memory: 2467  loss: 2.1419  grad_norm: 0.3402
[2025-07-31 13:09:22]    07/31 13:09:22 - mmengine - INFO - Epoch(val) [2][2/2]    auc: 1.5992  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0270  data_time: 0.0736  time: 0.3582
[2025-07-31 13:09:23]    07/31 13:09:23 - mmengine - INFO - The best checkpoint with 1.5992 auc at 2 epoch is saved to best_auc_epoch_2.pth.
[2025-07-31 13:09:23]    07/31 13:09:23 - mmengine - INFO - Epoch(train)  [3][2/2]  lr: 1.0000e-03  eta: 0:00:11  time: 0.2504  data_time: 0.0597  memory: 2507  loss: 2.1183  grad_norm: 0.3402
[2025-07-31 13:09:23]    07/31 13:09:23 - mmengine - INFO - Epoch(train)  [4][2/2]  lr: 1.0000e-03  eta: 0:00:09  time: 0.2250  data_time: 0.0551  memory: 2507  loss: 2.1093  grad_norm: 0.4741
[2025-07-31 13:09:24]    07/31 13:09:24 - mmengine - INFO - Epoch(val) [4][2/2]    auc: 1.9095  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0286  data_time: 0.0108  time: 0.2727
[2025-07-31 13:09:24]    07/31 13:09:24 - mmengine - INFO - The previous best checkpoint /home/<USER>/johnny_ws/mmaction2_ws/work_dirs/bmn_exp1_2_batch_opt/best_auc_epoch_2.pth is removed
[2025-07-31 13:09:24]    07/31 13:09:24 - mmengine - INFO - The best checkpoint with 1.9095 auc at 4 epoch is saved to best_auc_epoch_4.pth.
[2025-07-31 13:09:25]    07/31 13:09:25 - mmengine - INFO - Epoch(train)  [5][2/2]  lr: 1.0000e-03  eta: 0:00:08  time: 0.2095  data_time: 0.0525  memory: 2507  loss: 2.0487  grad_norm: 0.4741
[2025-07-31 13:09:25]    07/31 13:09:25 - mmengine - INFO - Saving checkpoint at 5 epochs
[2025-07-31 13:09:25]    07/31 13:09:25 - mmengine - INFO - Epoch(train)  [6][2/2]  lr: 1.0000e-03  eta: 0:00:07  time: 0.1992  data_time: 0.0507  memory: 2507  loss: 2.0081  grad_norm: 1.9383
[2025-07-31 13:09:26]    07/31 13:09:26 - mmengine - INFO - Epoch(val) [6][2/2]    auc: 1.9095  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0286  data_time: 0.0096  time: 0.2729
[2025-07-31 13:09:26]    07/31 13:09:26 - mmengine - INFO - Epoch(train)  [7][2/2]  lr: 1.0000e-03  eta: 0:00:06  time: 0.1917  data_time: 0.0495  memory: 2507  loss: 1.9832  grad_norm: 1.9383
[2025-07-31 13:09:27]    07/31 13:09:27 - mmengine - INFO - Epoch(train)  [8][2/2]  lr: 1.0000e-03  eta: 0:00:06  time: 0.1863  data_time: 0.0487  memory: 2507  loss: 1.9645  grad_norm: 2.5505
[2025-07-31 13:09:27]    07/31 13:09:27 - mmengine - INFO - Epoch(val) [8][2/2]    auc: 1.7341  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0270  data_time: 0.0104  time: 0.2749
[2025-07-31 13:09:28]    07/31 13:09:28 - mmengine - INFO - Epoch(train)  [9][2/2]  lr: 1.0000e-03  eta: 0:00:05  time: 0.1821  data_time: 0.0480  memory: 2507  loss: 1.9463  grad_norm: 2.5505
[2025-07-31 13:09:28]    07/31 13:09:28 - mmengine - INFO - Epoch(train) [10][2/2]  lr: 1.0000e-03  eta: 0:00:05  time: 0.1785  data_time: 0.0473  memory: 2507  loss: 1.9360  grad_norm: 2.3232
[2025-07-31 13:09:28]    07/31 13:09:28 - mmengine - INFO - Saving checkpoint at 10 epochs
[2025-07-31 13:09:29]    07/31 13:09:29 - mmengine - INFO - Epoch(val) [10][2/2]    auc: 1.8651  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0286  data_time: 0.0086  time: 0.2704
[2025-07-31 13:09:29]    07/31 13:09:29 - mmengine - INFO - Epoch(train) [11][2/2]  lr: 1.0000e-03  eta: 0:00:04  time: 0.1513  data_time: 0.0421  memory: 2507  loss: 1.8966  grad_norm: 2.3232
[2025-07-31 13:09:30]    07/31 13:09:30 - mmengine - INFO - Epoch(train) [12][2/2]  lr: 1.0000e-03  eta: 0:00:04  time: 0.1483  data_time: 0.0421  memory: 2507  loss: 1.8417  grad_norm: 2.2395
[2025-07-31 13:09:30]    07/31 13:09:30 - mmengine - INFO - Epoch(val) [12][2/2]    auc: 1.8873  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0286  data_time: 0.0083  time: 0.2681
[2025-07-31 13:09:30]    07/31 13:09:30 - mmengine - INFO - Epoch(train) [13][2/2]  lr: 1.0000e-03  eta: 0:00:04  time: 0.1479  data_time: 0.0420  memory: 2507  loss: 1.8102  grad_norm: 2.2395
[2025-07-31 13:09:31]    07/31 13:09:31 - mmengine - INFO - Epoch(train) [14][2/2]  lr: 1.0000e-03  eta: 0:00:03  time: 0.1478  data_time: 0.0420  memory: 2507  loss: 1.7781  grad_norm: 2.6960
[2025-07-31 13:09:31]    07/31 13:09:31 - mmengine - INFO - Epoch(val) [14][2/2]    auc: 1.8460  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0286  data_time: 0.0077  time: 0.2666
[2025-07-31 13:09:32]    07/31 13:09:32 - mmengine - INFO - Epoch(train) [15][2/2]  lr: 1.0000e-03  eta: 0:00:03  time: 0.1478  data_time: 0.0420  memory: 2507  loss: 1.7665  grad_norm: 2.6960
[2025-07-31 13:09:32]    07/31 13:09:32 - mmengine - INFO - Saving checkpoint at 15 epochs
[2025-07-31 13:09:33]    07/31 13:09:33 - mmengine - INFO - Epoch(train) [16][2/2]  lr: 3.0000e-04  eta: 0:00:03  time: 0.1480  data_time: 0.0421  memory: 2507  loss: 1.7592  grad_norm: 3.0064
[2025-07-31 13:09:34]    07/31 13:09:34 - mmengine - INFO - Epoch(val) [16][2/2]    auc: 1.8056  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0270  data_time: 0.0084  time: 0.2657
[2025-07-31 13:09:34]    07/31 13:09:34 - mmengine - INFO - Epoch(train) [17][2/2]  lr: 3.0000e-04  eta: 0:00:02  time: 0.1481  data_time: 0.0422  memory: 2507  loss: 1.7436  grad_norm: 3.0064
[2025-07-31 13:09:35]    07/31 13:09:35 - mmengine - INFO - Epoch(train) [18][2/2]  lr: 3.0000e-04  eta: 0:00:02  time: 0.1479  data_time: 0.0419  memory: 2507  loss: 1.7238  grad_norm: 3.1836
[2025-07-31 13:09:35]    07/31 13:09:35 - mmengine - INFO - Epoch(val) [18][2/2]    auc: 1.7310  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0270  data_time: 0.0081  time: 0.2665
[2025-07-31 13:09:35]    07/31 13:09:35 - mmengine - INFO - Epoch(train) [19][2/2]  lr: 3.0000e-04  eta: 0:00:01  time: 0.1476  data_time: 0.0418  memory: 2507  loss: 1.7150  grad_norm: 3.1836
[2025-07-31 13:09:36]    07/31 13:09:36 - mmengine - INFO - Epoch(train) [20][2/2]  lr: 3.0000e-04  eta: 0:00:01  time: 0.1475  data_time: 0.0417  memory: 2507  loss: 1.6896  grad_norm: 3.1563
[2025-07-31 13:09:36]    07/31 13:09:36 - mmengine - INFO - Saving checkpoint at 20 epochs
[2025-07-31 13:09:38]    07/31 13:09:38 - mmengine - INFO - Epoch(val) [20][2/2]    auc: 1.6722  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0270  data_time: 0.0088  time: 0.2694
[2025-07-31 13:09:38]    07/31 13:09:38 - mmengine - INFO - Epoch(train) [21][2/2]  lr: 9.0000e-05  eta: 0:00:01  time: 0.1471  data_time: 0.0416  memory: 2507  loss: 1.6785  grad_norm: 3.1563
[2025-07-31 13:09:39]    07/31 13:09:39 - mmengine - INFO - Epoch(train) [22][2/2]  lr: 9.0000e-05  eta: 0:00:00  time: 0.1468  data_time: 0.0415  memory: 2507  loss: 1.6883  grad_norm: 3.0350
[2025-07-31 13:09:39]    07/31 13:09:39 - mmengine - INFO - Epoch(val) [22][2/2]    auc: 1.6563  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0270  data_time: 0.0084  time: 0.2667
[2025-07-31 13:09:39]    07/31 13:09:39 - mmengine - INFO - Epoch(train) [23][2/2]  lr: 9.0000e-05  eta: 0:00:00  time: 0.1466  data_time: 0.0415  memory: 2507  loss: 1.6760  grad_norm: 3.0350
[2025-07-31 13:09:40]    07/31 13:09:40 - mmengine - INFO - Epoch(train) [24][2/2]  lr: 9.0000e-05  eta: 0:00:00  time: 0.1469  data_time: 0.0419  memory: 2507  loss: 1.6675  grad_norm: 2.8871
[2025-07-31 13:09:40]    07/31 13:09:40 - mmengine - INFO - Epoch(val) [24][2/2]    auc: 1.6563  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0270  data_time: 0.0105  time: 0.2670
[2025-07-31 13:09:41]    07/31 13:09:41 - mmengine - INFO - Epoch(train) [25][2/2]  lr: 9.0000e-05  eta: 0:00:00  time: 0.1469  data_time: 0.0419  memory: 2507  loss: 1.6528  grad_norm: 2.7152
[2025-07-31 13:09:41]    07/31 13:09:41 - mmengine - INFO - Saving checkpoint at 25 epochs
[2025-07-31 13:09:43]    07/31 13:09:43 - mmengine - INFO - Epoch(val) [25][2/2]    auc: 1.6595  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0270  data_time: 0.0107  time: 0.2692
[2025-07-31 13:09:44] 
🎉 实验 exp1_2_batch_optimization 完成! 耗时: 0.5分钟
[2025-07-31 13:09:44] ✅ 实验 exp1_2_batch_optimization.py 成功完成
[2025-07-31 13:09:44] 📈 关键指标:
[2025-07-31 13:09:44]    ar@1: 0.0
[2025-07-31 13:09:44]    ar@10: 0.0
[2025-07-31 13:09:44]    loss: 1.6528
[2025-07-31 13:09:44]    auc: 1.6595
[2025-07-31 13:09:44]    ar@5: 0.0
[2025-07-31 13:09:44] 💾 实验结果已保存到: ../../../experiment_results/experiment_results_20250731_130817.json
[2025-07-31 13:09:44] 
============================================================
[2025-07-31 13:09:44] 📊 实验进度: 3/3 - exp2_1_boundary_optimization.py
[2025-07-31 13:09:44] ============================================================
[2025-07-31 13:09:44] 📝 实验描述: 实验2.1: 边界参数优化配置
目标: 优化boundary_ratio和num_samples参数，提高边界检测精度
预期效果: 显著改善AR@1/5/10指标，提高精确定位能力...
[2025-07-31 13:09:44] 🔧 关键参数: {'boundary_ratio': '0.3', 'num_samples': '64', 'lr': '0.0008', 'max_epochs': '35'}
[2025-07-31 13:09:44] 
🚀 开始实验: exp2_1_boundary_optimization
[2025-07-31 13:09:44]    配置文件: ../configs/exp2_1_boundary_optimization.py
[2025-07-31 13:09:44]    GPU设备: 0
[2025-07-31 13:09:44]    执行命令: python /home/<USER>/johnny_ws/mmaction2_ws/data_process/training/scripts/../../../mmaction2/tools/train.py /home/<USER>/johnny_ws/mmaction2_ws/data_process/training/scripts/../configs/exp2_1_boundary_optimization.py
[2025-07-31 13:09:44] ============================================================
[2025-07-31 13:09:47]    '显著改善AR@1/5/10指标',
[2025-07-31 13:09:47]    '保持或提高AUC',
[2025-07-31 13:09:47]    'AR@1 > 0.02',
[2025-07-31 13:09:47]    'AR@5 > 0.08',
[2025-07-31 13:09:47]    'AR@10 > 0.15',
[2025-07-31 13:09:47]    'AR@100 > 0.04',
[2025-07-31 13:09:47]    'AUC > 2.0',
[2025-07-31 13:09:47]    log_processor = dict(by_epoch=True, type='LogProcessor', window_size=20)
[2025-07-31 13:09:47]    max_epochs = 35
[2025-07-31 13:09:47]    by_epoch=True,
[2025-07-31 13:09:47]    metric_type='AR@AN',
[2025-07-31 13:09:47]    max_epochs=35, type='EpochBasedTrainLoop', val_begin=1, val_interval=2)
[2025-07-31 13:09:47]    metric_type='AR@AN',
[2025-07-31 13:09:53]    before_train_epoch:
[2025-07-31 13:09:53]    after_train_epoch:
[2025-07-31 13:09:53]    before_val_epoch:
[2025-07-31 13:09:53]    after_val_epoch:
[2025-07-31 13:09:53]    before_test_epoch:
[2025-07-31 13:09:53]    after_test_epoch:
[2025-07-31 13:09:54]    07/31 13:09:54 - mmengine - INFO - Epoch(train)  [1][2/2]  lr: 8.0000e-04  eta: 0:00:32  time: 0.4796  data_time: 0.0884  memory: 8195  grad_norm: 0.3660  loss: 2.0942
[2025-07-31 13:09:55]    07/31 13:09:55 - mmengine - INFO - Epoch(train)  [2][2/2]  lr: 8.0000e-04  eta: 0:00:23  time: 0.3549  data_time: 0.0651  memory: 8195  grad_norm: 2.7440  loss: 1.9836
[2025-07-31 13:09:56]    07/31 13:09:56 - mmengine - INFO - Epoch(val) [2][2/2]    auc: 2.0746  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0286  data_time: 0.0691  time: 0.5839
[2025-07-31 13:10:00]    07/31 13:10:00 - mmengine - INFO - The best checkpoint with 2.0746 auc at 2 epoch is saved to best_auc_epoch_2.pth.
[2025-07-31 13:10:00]    07/31 13:10:00 - mmengine - INFO - Epoch(train)  [3][2/2]  lr: 8.0000e-04  eta: 0:00:20  time: 0.3128  data_time: 0.0569  memory: 8195  grad_norm: 5.1267  loss: 1.9607
[2025-07-31 13:10:01]    07/31 13:10:01 - mmengine - INFO - Epoch(train)  [4][2/2]  lr: 8.0000e-04  eta: 0:00:18  time: 0.2914  data_time: 0.0530  memory: 8195  grad_norm: 4.2255  loss: 1.9263
[2025-07-31 13:10:02]    07/31 13:10:02 - mmengine - INFO - Epoch(val) [4][2/2]    auc: 1.8762  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0286  data_time: 0.0102  time: 0.5237
[2025-07-31 13:10:02]    07/31 13:10:02 - mmengine - INFO - Epoch(train)  [5][2/2]  lr: 8.0000e-04  eta: 0:00:16  time: 0.2785  data_time: 0.0505  memory: 8195  grad_norm: 3.7314  loss: 1.9064
[2025-07-31 13:10:02]    07/31 13:10:02 - mmengine - INFO - Saving checkpoint at 5 epochs
[2025-07-31 13:10:06]    07/31 13:10:06 - mmengine - INFO - Epoch(train)  [6][2/2]  lr: 8.0000e-04  eta: 0:00:15  time: 0.2712  data_time: 0.0494  memory: 8195  grad_norm: 3.4321  loss: 1.8827
[2025-07-31 13:10:07]    07/31 13:10:07 - mmengine - INFO - Epoch(val) [6][2/2]    auc: 1.6333  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0286  data_time: 0.0095  time: 0.5207
[2025-07-31 13:10:07]    07/31 13:10:07 - mmengine - INFO - Epoch(train)  [7][2/2]  lr: 8.0000e-04  eta: 0:00:14  time: 0.2647  data_time: 0.0482  memory: 8195  grad_norm: 3.1220  loss: 1.8558
[2025-07-31 13:10:08]    07/31 13:10:08 - mmengine - INFO - Epoch(train)  [8][2/2]  lr: 8.0000e-04  eta: 0:00:14  time: 0.2603  data_time: 0.0475  memory: 8195  grad_norm: 2.8947  loss: 1.8224
[2025-07-31 13:10:08]    07/31 13:10:08 - mmengine - INFO - Epoch(val) [8][2/2]    auc: 0.2556  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0032  data_time: 0.0089  time: 0.3523
[2025-07-31 13:10:09]    07/31 13:10:09 - mmengine - INFO - Epoch(train)  [9][2/2]  lr: 8.0000e-04  eta: 0:00:13  time: 0.2568  data_time: 0.0469  memory: 8195  grad_norm: 2.6807  loss: 1.7989
[2025-07-31 13:10:09]    07/31 13:10:09 - mmengine - INFO - Epoch(train) [10][2/2]  lr: 8.0000e-04  eta: 0:00:12  time: 0.2543  data_time: 0.0465  memory: 8195  grad_norm: 2.5389  loss: 1.7769
[2025-07-31 13:10:09]    07/31 13:10:09 - mmengine - INFO - Saving checkpoint at 10 epochs
[2025-07-31 13:10:13]    07/31 13:10:13 - mmengine - INFO - Epoch(val) [10][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0088  time: 0.2255
[2025-07-31 13:10:13]    07/31 13:10:13 - mmengine - INFO - Epoch(train) [11][2/2]  lr: 8.0000e-04  eta: 0:00:12  time: 0.2295  data_time: 0.0418  memory: 8195  grad_norm: 2.5923  loss: 1.7127
[2025-07-31 13:10:13]    07/31 13:10:13 - mmengine - INFO - Epoch(train) [12][2/2]  lr: 8.0000e-04  eta: 0:00:11  time: 0.2292  data_time: 0.0418  memory: 8195  grad_norm: 2.2297  loss: 1.6677
[2025-07-31 13:10:14]    07/31 13:10:14 - mmengine - INFO - Epoch(val) [12][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0091  time: 0.2125
[2025-07-31 13:10:14]    07/31 13:10:14 - mmengine - INFO - Epoch(train) [13][2/2]  lr: 8.0000e-04  eta: 0:00:10  time: 0.2291  data_time: 0.0418  memory: 8195  grad_norm: 1.7599  loss: 1.6180
[2025-07-31 13:10:15]    07/31 13:10:15 - mmengine - INFO - Epoch(train) [14][2/2]  lr: 8.0000e-04  eta: 0:00:10  time: 0.2292  data_time: 0.0418  memory: 8195  grad_norm: 2.0416  loss: 1.5763
[2025-07-31 13:10:15]    07/31 13:10:15 - mmengine - INFO - Epoch(val) [14][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0097  time: 0.1552
[2025-07-31 13:10:15]    07/31 13:10:15 - mmengine - INFO - Epoch(train) [15][2/2]  lr: 8.0000e-04  eta: 0:00:09  time: 0.2292  data_time: 0.0419  memory: 8195  grad_norm: 2.1000  loss: 1.5207
[2025-07-31 13:10:15]    07/31 13:10:15 - mmengine - INFO - Saving checkpoint at 15 epochs
[2025-07-31 13:10:19]    07/31 13:10:19 - mmengine - INFO - Epoch(train) [16][2/2]  lr: 4.0000e-04  eta: 0:00:09  time: 0.2291  data_time: 0.0419  memory: 8195  grad_norm: 2.3524  loss: 1.4746
[2025-07-31 13:10:20]    07/31 13:10:20 - mmengine - INFO - Epoch(val) [16][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0104  time: 0.1017
[2025-07-31 13:10:20]    07/31 13:10:20 - mmengine - INFO - Epoch(train) [17][2/2]  lr: 4.0000e-04  eta: 0:00:08  time: 0.2294  data_time: 0.0419  memory: 8195  grad_norm: 2.3794  loss: 1.4286
[2025-07-31 13:10:21]    07/31 13:10:21 - mmengine - INFO - Epoch(train) [18][2/2]  lr: 4.0000e-04  eta: 0:00:08  time: 0.2292  data_time: 0.0416  memory: 8195  grad_norm: 2.4521  loss: 1.3920
[2025-07-31 13:10:21]    07/31 13:10:21 - mmengine - INFO - Epoch(val) [18][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0088  time: 0.1028
[2025-07-31 13:10:21]    07/31 13:10:21 - mmengine - INFO - Epoch(train) [19][2/2]  lr: 4.0000e-04  eta: 0:00:07  time: 0.2291  data_time: 0.0415  memory: 8195  grad_norm: 2.6085  loss: 1.3507
[2025-07-31 13:10:22]    07/31 13:10:22 - mmengine - INFO - Epoch(train) [20][2/2]  lr: 4.0000e-04  eta: 0:00:07  time: 0.2288  data_time: 0.0413  memory: 8195  grad_norm: 2.8240  loss: 1.3047
[2025-07-31 13:10:22]    07/31 13:10:22 - mmengine - INFO - Saving checkpoint at 20 epochs
[2025-07-31 13:10:25]    07/31 13:10:25 - mmengine - INFO - Epoch(val) [20][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0095  time: 0.0984
[2025-07-31 13:10:26]    07/31 13:10:26 - mmengine - INFO - Epoch(train) [21][2/2]  lr: 4.0000e-04  eta: 0:00:06  time: 0.2287  data_time: 0.0412  memory: 8195  grad_norm: 2.9079  loss: 1.2618
[2025-07-31 13:10:26]    07/31 13:10:26 - mmengine - INFO - Epoch(train) [22][2/2]  lr: 4.0000e-04  eta: 0:00:06  time: 0.2288  data_time: 0.0410  memory: 8195  grad_norm: 3.1381  loss: 1.2338
[2025-07-31 13:10:26]    07/31 13:10:26 - mmengine - INFO - Epoch(val) [22][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0106  time: 0.1042
[2025-07-31 13:10:27]    07/31 13:10:27 - mmengine - INFO - Epoch(train) [23][2/2]  lr: 4.0000e-04  eta: 0:00:05  time: 0.2286  data_time: 0.0408  memory: 8195  grad_norm: 3.0104  loss: 1.2013
[2025-07-31 13:10:27]    07/31 13:10:27 - mmengine - INFO - Epoch(train) [24][2/2]  lr: 4.0000e-04  eta: 0:00:05  time: 0.2284  data_time: 0.0406  memory: 8195  grad_norm: 2.9491  loss: 1.1689
[2025-07-31 13:10:27]    07/31 13:10:27 - mmengine - INFO - Epoch(val) [24][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0093  time: 0.0778
[2025-07-31 13:10:28]    07/31 13:10:28 - mmengine - INFO - Epoch(train) [25][2/2]  lr: 4.0000e-04  eta: 0:00:04  time: 0.2286  data_time: 0.0405  memory: 8195  grad_norm: 2.9326  loss: 1.1461
[2025-07-31 13:10:28]    07/31 13:10:28 - mmengine - INFO - Saving checkpoint at 25 epochs
[2025-07-31 13:10:32]    07/31 13:10:32 - mmengine - INFO - Epoch(train) [26][2/2]  lr: 2.0000e-04  eta: 0:00:04  time: 0.2283  data_time: 0.0404  memory: 8195  grad_norm: 2.8479  loss: 1.1205
[2025-07-31 13:10:32]    07/31 13:10:32 - mmengine - INFO - Epoch(val) [26][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0088  time: 0.0590
[2025-07-31 13:10:32]    07/31 13:10:32 - mmengine - INFO - Epoch(train) [27][2/2]  lr: 2.0000e-04  eta: 0:00:03  time: 0.2285  data_time: 0.0404  memory: 8195  grad_norm: 2.8506  loss: 1.0948
[2025-07-31 13:10:33]    07/31 13:10:33 - mmengine - INFO - Epoch(train) [28][2/2]  lr: 2.0000e-04  eta: 0:00:03  time: 0.2287  data_time: 0.0404  memory: 8195  grad_norm: 3.0659  loss: 1.0726
[2025-07-31 13:10:33]    07/31 13:10:33 - mmengine - INFO - Epoch(val) [28][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0086  time: 0.0835
[2025-07-31 13:10:33]    07/31 13:10:33 - mmengine - INFO - Epoch(train) [29][2/2]  lr: 2.0000e-04  eta: 0:00:02  time: 0.2283  data_time: 0.0400  memory: 8195  grad_norm: 3.0690  loss: 1.0495
[2025-07-31 13:10:34]    07/31 13:10:34 - mmengine - INFO - Epoch(train) [30][2/2]  lr: 2.0000e-04  eta: 0:00:02  time: 0.2280  data_time: 0.0399  memory: 8195  grad_norm: 2.9887  loss: 1.0282
[2025-07-31 13:10:34]    07/31 13:10:34 - mmengine - INFO - Saving checkpoint at 30 epochs
[2025-07-31 13:10:37]    07/31 13:10:37 - mmengine - INFO - Epoch(val) [30][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0086  time: 0.0810
[2025-07-31 13:10:38]    07/31 13:10:38 - mmengine - INFO - Epoch(train) [31][2/2]  lr: 1.0000e-04  eta: 0:00:01  time: 0.2282  data_time: 0.0399  memory: 8195  grad_norm: 3.1222  loss: 1.0202
[2025-07-31 13:10:38]    07/31 13:10:38 - mmengine - INFO - Epoch(train) [32][2/2]  lr: 1.0000e-04  eta: 0:00:01  time: 0.2287  data_time: 0.0402  memory: 8195  grad_norm: 2.9506  loss: 0.9935
[2025-07-31 13:10:38]    07/31 13:10:38 - mmengine - INFO - Epoch(val) [32][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0082  time: 0.0437
[2025-07-31 13:10:39]    07/31 13:10:39 - mmengine - INFO - Epoch(train) [33][2/2]  lr: 1.0000e-04  eta: 0:00:00  time: 0.2292  data_time: 0.0405  memory: 8195  grad_norm: 2.7768  loss: 0.9670
[2025-07-31 13:10:39]    07/31 13:10:39 - mmengine - INFO - Epoch(train) [34][2/2]  lr: 1.0000e-04  eta: 0:00:00  time: 0.2296  data_time: 0.0407  memory: 8195  grad_norm: 2.6712  loss: 0.9503
[2025-07-31 13:10:39]    07/31 13:10:39 - mmengine - INFO - Epoch(val) [34][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0081  time: 0.0483
[2025-07-31 13:10:40]    07/31 13:10:40 - mmengine - INFO - Epoch(train) [35][2/2]  lr: 1.0000e-04  eta: 0:00:00  time: 0.2296  data_time: 0.0406  memory: 8195  grad_norm: 2.7323  loss: 0.9343
[2025-07-31 13:10:40]    07/31 13:10:40 - mmengine - INFO - Saving checkpoint at 35 epochs
[2025-07-31 13:10:43]    07/31 13:10:43 - mmengine - INFO - Epoch(val) [35][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0088  time: 0.0532
[2025-07-31 13:10:44] 
🎉 实验 exp2_1_boundary_optimization 完成! 耗时: 1.0分钟
[2025-07-31 13:10:44] ✅ 实验 exp2_1_boundary_optimization.py 成功完成
[2025-07-31 13:10:44] 📈 关键指标:
[2025-07-31 13:10:44]    ar@1: 0.0
[2025-07-31 13:10:44]    ar@10: 0.0
[2025-07-31 13:10:44]    loss: 0.9343
[2025-07-31 13:10:44]    auc: 0.0
[2025-07-31 13:10:44]    ar@5: 0.0
[2025-07-31 13:10:44] 💾 实验结果已保存到: ../../../experiment_results/experiment_results_20250731_130817.json
[2025-07-31 13:10:44] 
================================================================================
[2025-07-31 13:10:44] 📊 实验汇总报告
[2025-07-31 13:10:44] ================================================================================
[2025-07-31 13:10:44] ⏱️  总耗时: 2.5 分钟
[2025-07-31 13:10:44] ✅ 成功实验: 3
[2025-07-31 13:10:44] ❌ 失败实验: 0
[2025-07-31 13:10:44] 📁 详细日志: ../../../experiment_results/experiment_log_20250731_130817.txt
[2025-07-31 13:10:44] 📄 结果文件: ../../../experiment_results/experiment_results_20250731_130817.json
[2025-07-31 13:10:44] 
🏆 成功实验指标对比:
[2025-07-31 13:10:44] --------------------------------------------------------------------------------
[2025-07-31 13:10:44] 实验名称                      AUC      AR@1     AR@5     AR@10    耗时(分)   
[2025-07-31 13:10:44] --------------------------------------------------------------------------------
[2025-07-31 13:10:44] exp1_1_data_augmentation  0.0000   0.0000   0.0000   0.0000   0.9     
[2025-07-31 13:10:44] exp1_2_batch_optimizatio  1.6595   0.0000   0.0000   0.0000   0.5     
[2025-07-31 13:10:44] exp2_1_boundary_optimiza  0.0000   0.0000   0.0000   0.0000   1.0     
[2025-07-31 13:10:44] 
🥇 最佳实验:
[2025-07-31 13:10:44]    最高AUC: exp1_2_batch_optimization (AUC: 1.6595)
[2025-07-31 13:10:44]    最高AR@1: exp1_1_data_augmentation (AR@1: 0.0000)
[2025-07-31 13:10:44] 
💡 后续建议:
[2025-07-31 13:10:44]    1. 分析最佳实验的参数设置
[2025-07-31 13:10:44]    2. 基于最佳结果进行进一步的参数微调
[2025-07-31 13:10:44]    3. 考虑组合多个有效的优化策略
[2025-07-31 13:10:44] ================================================================================
