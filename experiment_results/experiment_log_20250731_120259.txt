[2025-07-31 12:02:59] 
================================================================================
[2025-07-31 12:02:59] 🎯 开始BMN自动化参数实验
[2025-07-31 12:02:59] ================================================================================
[2025-07-31 12:02:59] 🔍 检查训练环境...
[2025-07-31 12:03:00] ✅ CUDA可用: NVIDIA RTX A5000
[2025-07-31 12:03:00]    GPU内存: 23.6GB
[2025-07-31 12:03:00] ✅ MMAction2已安装: 1.2.0
[2025-07-31 12:03:00] 
📁 检查数据文件...
[2025-07-31 12:03:00] ✅ 标注文件: ../../../data/MultiClassTAD/multiclass_tad_train.json
[2025-07-31 12:03:00] ✅ 标注文件: ../../../data/MultiClassTAD/multiclass_tad_val.json
[2025-07-31 12:03:00] ✅ 特征目录: ../../../data/MultiClassTAD/features_slowonly (10 个CSV文件)
[2025-07-31 12:03:00] 
📋 扫描实验配置文件...
[2025-07-31 12:03:00] ✅ 找到 3 个实验配置文件:
[2025-07-31 12:03:00]    1. exp1_1_data_augmentation.py
[2025-07-31 12:03:00]    2. exp1_2_batch_optimization.py
[2025-07-31 12:03:00]    3. exp2_1_boundary_optimization.py
[2025-07-31 12:03:00] 
============================================================
[2025-07-31 12:03:00] 📊 实验进度: 1/3 - exp1_1_data_augmentation.py
[2025-07-31 12:03:00] ============================================================
[2025-07-31 12:03:00] 📝 实验描述: 实验1.1: 数据增强优化配置
目标: 通过时间域数据增强解决数据集过小的问题
预期效果: 提高模型泛化能力，改善AR@1/5/10指标...
[2025-07-31 12:03:00] 🔧 关键参数: {'boundary_ratio': '0.5', 'num_samples': '32', 'batch_size': '8', 'lr': '0.0005', 'max_epochs': '30'}
[2025-07-31 12:03:00] 
🚀 开始实验: exp1_1_data_augmentation
[2025-07-31 12:03:00]    配置文件: ../configs/exp1_1_data_augmentation.py
[2025-07-31 12:03:00]    GPU设备: 0
[2025-07-31 12:03:00]    执行命令: python /home/<USER>/johnny_ws/mmaction2_ws/data_process/training/scripts/../../../mmaction2/tools/train.py /home/<USER>/johnny_ws/mmaction2_ws/data_process/training/scripts/../configs/exp1_1_data_augmentation.py
[2025-07-31 12:03:00] ============================================================
[2025-07-31 12:03:03]    '改善AR@1/5/10指标',
[2025-07-31 12:03:03]    'AR@1 > 0.01',
[2025-07-31 12:03:03]    'AR@5 > 0.05',
[2025-07-31 12:03:03]    'AR@10 > 0.10',
[2025-07-31 12:03:03]    'AUC保持在2.0以上',
[2025-07-31 12:03:03]    log_processor = dict(by_epoch=True, type='LogProcessor', window_size=20)
[2025-07-31 12:03:03]    max_epochs = 30
[2025-07-31 12:03:03]    by_epoch=True,
[2025-07-31 12:03:03]    metric_type='AR@AN',
[2025-07-31 12:03:03]    max_epochs=30, type='EpochBasedTrainLoop', val_begin=1, val_interval=2)
[2025-07-31 12:03:03]    metric_type='AR@AN',
[2025-07-31 12:03:06]    before_train_epoch:
[2025-07-31 12:03:06]    after_train_epoch:
[2025-07-31 12:03:06]    before_val_epoch:
[2025-07-31 12:03:06]    after_val_epoch:
[2025-07-31 12:03:06]    before_test_epoch:
[2025-07-31 12:03:06]    after_test_epoch:
[2025-07-31 12:03:08]    07/31 12:03:08 - mmengine - INFO - Epoch(train)  [1][1/1]  lr: 5.0000e-04  eta: 0:00:40  time: 1.4012  data_time: 0.4240  memory: 8060  grad_norm: 0.3320  loss: 2.1322
[2025-07-31 12:03:10]    07/31 12:03:10 - mmengine - INFO - Epoch(train)  [2][1/1]  lr: 5.0000e-04  eta: 0:00:35  time: 1.2582  data_time: 0.4714  memory: 8110  grad_norm: 0.3344  loss: 2.1231
[2025-07-31 12:03:10]    07/31 12:03:10 - mmengine - INFO - Epoch(val) [2][2/2]    auc: 1.6849  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0270  data_time: 0.0640  time: 0.3259
[2025-07-31 12:03:11]    07/31 12:03:11 - mmengine - INFO - The best checkpoint with 1.6849 auc at 2 epoch is saved to best_auc_epoch_2.pth.
[2025-07-31 12:03:11]    07/31 12:03:11 - mmengine - INFO - Epoch(train)  [3][1/1]  lr: 5.0000e-04  eta: 0:00:29  time: 1.0875  data_time: 0.3641  memory: 8110  grad_norm: 0.4096  loss: 2.1078
[2025-07-31 12:03:12]    07/31 12:03:12 - mmengine - INFO - Epoch(train)  [4][1/1]  lr: 5.0000e-04  eta: 0:00:26  time: 1.0008  data_time: 0.3113  memory: 8110  grad_norm: 0.5991  loss: 2.0701
[2025-07-31 12:03:13]    07/31 12:03:13 - mmengine - INFO - Epoch(val) [4][2/2]    auc: 1.9270  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0286  data_time: 0.0083  time: 0.2638
[2025-07-31 12:03:13]    07/31 12:03:13 - mmengine - INFO - The previous best checkpoint /home/<USER>/johnny_ws/mmaction2_ws/work_dirs/bmn_exp1_1_data_aug/best_auc_epoch_2.pth is removed
[2025-07-31 12:03:14]    07/31 12:03:14 - mmengine - INFO - The best checkpoint with 1.9270 auc at 4 epoch is saved to best_auc_epoch_4.pth.
[2025-07-31 12:03:15]    07/31 12:03:15 - mmengine - INFO - Epoch(train)  [5][1/1]  lr: 5.0000e-04  eta: 0:00:24  time: 0.9858  data_time: 0.3164  memory: 8110  grad_norm: 0.6414  loss: 2.0164
[2025-07-31 12:03:15]    07/31 12:03:15 - mmengine - INFO - Saving checkpoint at 5 epochs
[2025-07-31 12:03:17]    07/31 12:03:17 - mmengine - INFO - Epoch(train)  [6][1/1]  lr: 5.0000e-04  eta: 0:00:24  time: 1.0358  data_time: 0.3790  memory: 8110  grad_norm: 2.5387  loss: 1.9940
[2025-07-31 12:03:17]    07/31 12:03:17 - mmengine - INFO - Epoch(val) [6][2/2]    auc: 1.9032  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0286  data_time: 0.0105  time: 0.2674
[2025-07-31 12:03:19]    07/31 12:03:19 - mmengine - INFO - Epoch(train)  [7][1/1]  lr: 5.0000e-04  eta: 0:00:25  time: 1.0957  data_time: 0.4493  memory: 8110  grad_norm: 3.6810  loss: 1.9717
[2025-07-31 12:03:20]    07/31 12:03:20 - mmengine - INFO - Epoch(train)  [8][1/1]  lr: 5.0000e-04  eta: 0:00:23  time: 1.0771  data_time: 0.4358  memory: 8110  grad_norm: 4.0340  loss: 1.9488
[2025-07-31 12:03:20]    07/31 12:03:20 - mmengine - INFO - Epoch(val) [8][2/2]    auc: 1.9000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0286  data_time: 0.0087  time: 0.2664
[2025-07-31 12:03:21]    07/31 12:03:21 - mmengine - INFO - Epoch(train)  [9][1/1]  lr: 5.0000e-04  eta: 0:00:22  time: 1.0621  data_time: 0.4272  memory: 8110  grad_norm: 3.8790  loss: 1.9281
[2025-07-31 12:03:22]    07/31 12:03:22 - mmengine - INFO - Epoch(train) [10][1/1]  lr: 5.0000e-04  eta: 0:00:20  time: 1.0460  data_time: 0.4168  memory: 8110  grad_norm: 3.7490  loss: 1.9106
[2025-07-31 12:03:22]    07/31 12:03:22 - mmengine - INFO - Saving checkpoint at 10 epochs
[2025-07-31 12:03:23]    07/31 12:03:23 - mmengine - INFO - Epoch(val) [10][2/2]    auc: 1.7952  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0286  data_time: 0.0088  time: 0.2658
[2025-07-31 12:03:24]    07/31 12:03:24 - mmengine - INFO - Epoch(train) [11][1/1]  lr: 5.0000e-04  eta: 0:00:19  time: 1.0164  data_time: 0.3915  memory: 8110  grad_norm: 3.5326  loss: 1.8949
[2025-07-31 12:03:25]    07/31 12:03:25 - mmengine - INFO - Epoch(train) [12][1/1]  lr: 5.0000e-04  eta: 0:00:18  time: 1.0521  data_time: 0.4299  memory: 8110  grad_norm: 3.3563  loss: 1.8823
[2025-07-31 12:03:26]    07/31 12:03:26 - mmengine - INFO - Epoch(val) [12][2/2]    auc: 1.7103  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0270  data_time: 0.0081  time: 0.2637
[2025-07-31 12:03:27]    07/31 12:03:27 - mmengine - INFO - Epoch(train) [13][1/1]  lr: 5.0000e-04  eta: 0:00:17  time: 1.0417  data_time: 0.4223  memory: 8110  grad_norm: 3.1563  loss: 1.8702
[2025-07-31 12:03:28]    07/31 12:03:28 - mmengine - INFO - Epoch(train) [14][1/1]  lr: 5.0000e-04  eta: 0:00:16  time: 1.0187  data_time: 0.4019  memory: 8110  grad_norm: 2.9711  loss: 1.8591
[2025-07-31 12:03:28]    07/31 12:03:28 - mmengine - INFO - Epoch(val) [14][2/2]    auc: 1.7230  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0270  data_time: 0.0081  time: 0.2628
[2025-07-31 12:03:29]    07/31 12:03:29 - mmengine - INFO - Epoch(train) [15][1/1]  lr: 5.0000e-04  eta: 0:00:15  time: 1.0230  data_time: 0.4086  memory: 8110  grad_norm: 2.8004  loss: 1.8473
[2025-07-31 12:03:29]    07/31 12:03:29 - mmengine - INFO - Saving checkpoint at 15 epochs
[2025-07-31 12:03:31]    07/31 12:03:31 - mmengine - INFO - Epoch(train) [16][1/1]  lr: 5.0000e-04  eta: 0:00:14  time: 1.0171  data_time: 0.4039  memory: 8110  grad_norm: 2.6892  loss: 1.8356
[2025-07-31 12:03:31]    07/31 12:03:31 - mmengine - INFO - Epoch(val) [16][2/2]    auc: 1.7833  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0270  data_time: 0.0082  time: 0.2529
[2025-07-31 12:03:32]    07/31 12:03:32 - mmengine - INFO - Epoch(train) [17][1/1]  lr: 5.0000e-04  eta: 0:00:13  time: 1.0215  data_time: 0.4096  memory: 8110  grad_norm: 2.5554  loss: 1.8243
[2025-07-31 12:03:34]    07/31 12:03:34 - mmengine - INFO - Epoch(train) [18][1/1]  lr: 5.0000e-04  eta: 0:00:12  time: 1.0357  data_time: 0.4247  memory: 8110  grad_norm: 2.4755  loss: 1.8129
[2025-07-31 12:03:34]    07/31 12:03:34 - mmengine - INFO - Epoch(val) [18][2/2]    auc: 1.4794  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0190  data_time: 0.0084  time: 0.2136
[2025-07-31 12:03:35]    07/31 12:03:35 - mmengine - INFO - Epoch(train) [19][1/1]  lr: 5.0000e-04  eta: 0:00:11  time: 1.0290  data_time: 0.4193  memory: 8110  grad_norm: 2.4102  loss: 1.8013
[2025-07-31 12:03:36]    07/31 12:03:36 - mmengine - INFO - Epoch(train) [20][1/1]  lr: 5.0000e-04  eta: 0:00:10  time: 1.0320  data_time: 0.4234  memory: 8110  grad_norm: 2.3250  loss: 1.7898
[2025-07-31 12:03:36]    07/31 12:03:36 - mmengine - INFO - Saving checkpoint at 20 epochs
[2025-07-31 12:03:38]    07/31 12:03:38 - mmengine - INFO - Epoch(val) [20][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0085  time: 0.1572
[2025-07-31 12:03:39]    07/31 12:03:39 - mmengine - INFO - Epoch(train) [21][1/1]  lr: 2.5000e-04  eta: 0:00:09  time: 0.9988  data_time: 0.4097  memory: 8110  grad_norm: 2.3513  loss: 1.7588
[2025-07-31 12:03:39]    07/31 12:03:39 - mmengine - INFO - Epoch(train) [22][1/1]  lr: 2.5000e-04  eta: 0:00:08  time: 0.9795  data_time: 0.3910  memory: 8110  grad_norm: 2.3714  loss: 1.7278
[2025-07-31 12:03:40]    07/31 12:03:40 - mmengine - INFO - Epoch(val) [22][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0098  time: 0.1184
[2025-07-31 12:03:41]    07/31 12:03:41 - mmengine - INFO - Epoch(train) [23][1/1]  lr: 2.5000e-04  eta: 0:00:07  time: 0.9966  data_time: 0.4086  memory: 8110  grad_norm: 2.3869  loss: 1.6991
[2025-07-31 12:03:42]    07/31 12:03:42 - mmengine - INFO - Epoch(train) [24][1/1]  lr: 2.5000e-04  eta: 0:00:06  time: 1.0142  data_time: 0.4264  memory: 8110  grad_norm: 2.4267  loss: 1.6745
[2025-07-31 12:03:42]    07/31 12:03:42 - mmengine - INFO - Epoch(val) [24][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0078  time: 0.1095
[2025-07-31 12:03:43]    07/31 12:03:43 - mmengine - INFO - Epoch(train) [25][1/1]  lr: 2.5000e-04  eta: 0:00:05  time: 1.0056  data_time: 0.4169  memory: 8110  grad_norm: 2.4264  loss: 1.6547
[2025-07-31 12:03:43]    07/31 12:03:43 - mmengine - INFO - Saving checkpoint at 25 epochs
[2025-07-31 12:03:46]    07/31 12:03:46 - mmengine - INFO - Epoch(train) [26][1/1]  lr: 1.2500e-04  eta: 0:00:04  time: 1.0136  data_time: 0.4251  memory: 8110  grad_norm: 1.8807  loss: 1.6305
[2025-07-31 12:03:46]    07/31 12:03:46 - mmengine - INFO - Epoch(val) [26][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0111  time: 0.0960
[2025-07-31 12:03:46]    07/31 12:03:46 - mmengine - INFO - Epoch(train) [27][1/1]  lr: 1.2500e-04  eta: 0:00:03  time: 0.9769  data_time: 0.3885  memory: 8110  grad_norm: 1.3851  loss: 1.6072
[2025-07-31 12:03:48]    07/31 12:03:48 - mmengine - INFO - Epoch(train) [28][1/1]  lr: 1.2500e-04  eta: 0:00:02  time: 0.9837  data_time: 0.3960  memory: 8110  grad_norm: 1.0855  loss: 1.5860
[2025-07-31 12:03:48]    07/31 12:03:48 - mmengine - INFO - Epoch(val) [28][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0105  time: 0.0731
[2025-07-31 12:03:49]    07/31 12:03:49 - mmengine - INFO - Epoch(train) [29][1/1]  lr: 1.2500e-04  eta: 0:00:01  time: 0.9906  data_time: 0.4029  memory: 8110  grad_norm: 0.9782  loss: 1.5654
[2025-07-31 12:03:50]    07/31 12:03:50 - mmengine - INFO - Epoch(train) [30][1/1]  lr: 1.2500e-04  eta: 0:00:00  time: 1.0000  data_time: 0.4121  memory: 8110  grad_norm: 0.8786  loss: 1.5436
[2025-07-31 12:03:50]    07/31 12:03:50 - mmengine - INFO - Saving checkpoint at 30 epochs
[2025-07-31 12:03:52]    07/31 12:03:52 - mmengine - INFO - Epoch(val) [30][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0094  time: 0.0671
[2025-07-31 12:03:53] 
🎉 实验 exp1_1_data_augmentation 完成! 耗时: 0.9分钟
[2025-07-31 12:03:53] ✅ 实验 exp1_1_data_augmentation.py 成功完成
[2025-07-31 12:03:53] 📈 关键指标:
[2025-07-31 12:03:53]    ar@1: 0.0
[2025-07-31 12:03:53]    loss: 1.5436
[2025-07-31 12:03:53]    auc: 0.0
[2025-07-31 12:03:53]    ar@5: 0.0
[2025-07-31 12:03:53]    ar@10: 0.0
[2025-07-31 12:03:53] 💾 实验结果已保存到: ../../../experiment_results/experiment_results_20250731_120259.json
[2025-07-31 12:03:53] 
============================================================
[2025-07-31 12:03:53] 📊 实验进度: 2/3 - exp1_2_batch_optimization.py
[2025-07-31 12:03:53] ============================================================
[2025-07-31 12:03:53] 📝 实验描述: 实验1.2: 批次大小优化配置
目标: 通过梯度累积模拟更大的批次大小，改善训练稳定性
预期效果: 提高训练稳定性，改善梯度估计质量...
[2025-07-31 12:03:53] 🔧 关键参数: {'boundary_ratio': '0.5', 'num_samples': '32', 'batch_size': '4', 'lr': '0.001', 'max_epochs': '25'}
[2025-07-31 12:03:53] 
🚀 开始实验: exp1_2_batch_optimization
[2025-07-31 12:03:53]    配置文件: ../configs/exp1_2_batch_optimization.py
[2025-07-31 12:03:53]    GPU设备: 0
[2025-07-31 12:03:53]    执行命令: python /home/<USER>/johnny_ws/mmaction2_ws/data_process/training/scripts/../../../mmaction2/tools/train.py /home/<USER>/johnny_ws/mmaction2_ws/data_process/training/scripts/../configs/exp1_2_batch_optimization.py
[2025-07-31 12:03:53] ============================================================
[2025-07-31 12:03:56]    'AR@100 > 0.035',
[2025-07-31 12:03:56]    'AUC > 2.1',
[2025-07-31 12:03:56]    log_processor = dict(by_epoch=True, type='LogProcessor', window_size=20)
[2025-07-31 12:03:56]    max_epochs = 25
[2025-07-31 12:03:56]    by_epoch=True,
[2025-07-31 12:03:56]    metric_type='AR@AN',
[2025-07-31 12:03:56]    max_epochs=25, type='EpochBasedTrainLoop', val_begin=1, val_interval=2)
[2025-07-31 12:03:56]    metric_type='AR@AN',
[2025-07-31 12:03:59]    before_train_epoch:
[2025-07-31 12:03:59]    after_train_epoch:
[2025-07-31 12:03:59]    before_val_epoch:
[2025-07-31 12:03:59]    after_val_epoch:
[2025-07-31 12:03:59]    before_test_epoch:
[2025-07-31 12:03:59]    after_test_epoch:
[2025-07-31 12:04:00]    self.loss_scaler = scaler_type()
[2025-07-31 12:04:00]    07/31 12:04:00 - mmengine - INFO - Epoch(train)  [1][2/2]  lr: 1.0000e-03  eta: 0:00:19  time: 0.4166  data_time: 0.0917  memory: 2467  loss: 2.1457
[2025-07-31 12:04:01]    07/31 12:04:01 - mmengine - INFO - Epoch(train)  [2][2/2]  lr: 1.0000e-03  eta: 0:00:13  time: 0.3006  data_time: 0.0675  memory: 2467  loss: 2.1302  grad_norm: 0.3310
[2025-07-31 12:04:02]    07/31 12:04:02 - mmengine - INFO - Epoch(val) [2][2/2]    auc: 1.5563  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0270  data_time: 0.0679  time: 0.3462
[2025-07-31 12:04:02]    07/31 12:04:02 - mmengine - INFO - The best checkpoint with 1.5563 auc at 2 epoch is saved to best_auc_epoch_2.pth.
[2025-07-31 12:04:02]    07/31 12:04:02 - mmengine - INFO - Epoch(train)  [3][2/2]  lr: 1.0000e-03  eta: 0:00:10  time: 0.2498  data_time: 0.0592  memory: 2507  loss: 2.1117  grad_norm: 0.3310
[2025-07-31 12:04:03]    07/31 12:04:03 - mmengine - INFO - Epoch(train)  [4][2/2]  lr: 1.0000e-03  eta: 0:00:09  time: 0.2237  data_time: 0.0548  memory: 2507  loss: 2.1063  grad_norm: 0.4340
[2025-07-31 12:04:03]    07/31 12:04:03 - mmengine - INFO - Epoch(val) [4][2/2]    auc: 1.8984  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0286  data_time: 0.0091  time: 0.2617
[2025-07-31 12:04:03]    07/31 12:04:03 - mmengine - INFO - The previous best checkpoint /home/<USER>/johnny_ws/mmaction2_ws/work_dirs/bmn_exp1_2_batch_opt/best_auc_epoch_2.pth is removed
[2025-07-31 12:04:04]    07/31 12:04:04 - mmengine - INFO - The best checkpoint with 1.8984 auc at 4 epoch is saved to best_auc_epoch_4.pth.
[2025-07-31 12:04:04]    07/31 12:04:04 - mmengine - INFO - Epoch(train)  [5][2/2]  lr: 1.0000e-03  eta: 0:00:08  time: 0.2081  data_time: 0.0520  memory: 2507  loss: 2.0551  grad_norm: 0.4340
[2025-07-31 12:04:04]    07/31 12:04:04 - mmengine - INFO - Saving checkpoint at 5 epochs
[2025-07-31 12:04:05]    07/31 12:04:05 - mmengine - INFO - Epoch(train)  [6][2/2]  lr: 1.0000e-03  eta: 0:00:07  time: 0.1980  data_time: 0.0504  memory: 2507  loss: 2.0130  grad_norm: 1.4679
[2025-07-31 12:04:05]    07/31 12:04:05 - mmengine - INFO - Epoch(val) [6][2/2]    auc: 1.9206  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0286  data_time: 0.0088  time: 0.2634
[2025-07-31 12:04:05]    07/31 12:04:05 - mmengine - INFO - The previous best checkpoint /home/<USER>/johnny_ws/mmaction2_ws/work_dirs/bmn_exp1_2_batch_opt/best_auc_epoch_4.pth is removed
[2025-07-31 12:04:06]    07/31 12:04:06 - mmengine - INFO - The best checkpoint with 1.9206 auc at 6 epoch is saved to best_auc_epoch_6.pth.
[2025-07-31 12:04:07]    07/31 12:04:07 - mmengine - INFO - Epoch(train)  [7][2/2]  lr: 1.0000e-03  eta: 0:00:06  time: 0.1905  data_time: 0.0492  memory: 2507  loss: 1.9943  grad_norm: 1.4679
[2025-07-31 12:04:07]    07/31 12:04:07 - mmengine - INFO - Epoch(train)  [8][2/2]  lr: 1.0000e-03  eta: 0:00:06  time: 0.1853  data_time: 0.0481  memory: 2507  loss: 1.9856  grad_norm: 2.9180
[2025-07-31 12:04:07]    07/31 12:04:07 - mmengine - INFO - Epoch(val) [8][2/2]    auc: 1.6754  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0270  data_time: 0.0087  time: 0.2627
[2025-07-31 12:04:08]    07/31 12:04:08 - mmengine - INFO - Epoch(train)  [9][2/2]  lr: 1.0000e-03  eta: 0:00:05  time: 0.1808  data_time: 0.0472  memory: 2507  loss: 1.9685  grad_norm: 2.9180
[2025-07-31 12:04:08]    07/31 12:04:08 - mmengine - INFO - Epoch(train) [10][2/2]  lr: 1.0000e-03  eta: 0:00:05  time: 0.1773  data_time: 0.0466  memory: 2507  loss: 1.9558  grad_norm: 2.5833
[2025-07-31 12:04:08]    07/31 12:04:08 - mmengine - INFO - Saving checkpoint at 10 epochs
[2025-07-31 12:04:09]    07/31 12:04:09 - mmengine - INFO - Epoch(val) [10][2/2]    auc: 1.7881  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0270  data_time: 0.0096  time: 0.2655
[2025-07-31 12:04:09]    07/31 12:04:09 - mmengine - INFO - Epoch(train) [11][2/2]  lr: 1.0000e-03  eta: 0:00:04  time: 0.1501  data_time: 0.0414  memory: 2507  loss: 1.9221  grad_norm: 2.5833
[2025-07-31 12:04:10]    07/31 12:04:10 - mmengine - INFO - Epoch(train) [12][2/2]  lr: 1.0000e-03  eta: 0:00:04  time: 0.1460  data_time: 0.0412  memory: 2507  loss: 1.8861  grad_norm: 2.4377
[2025-07-31 12:04:10]    07/31 12:04:10 - mmengine - INFO - Epoch(val) [12][2/2]    auc: 1.8548  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0270  data_time: 0.0096  time: 0.2682
[2025-07-31 12:04:11]    07/31 12:04:11 - mmengine - INFO - Epoch(train) [13][2/2]  lr: 1.0000e-03  eta: 0:00:04  time: 0.1456  data_time: 0.0410  memory: 2507  loss: 1.8500  grad_norm: 2.4377
[2025-07-31 12:04:11]    07/31 12:04:11 - mmengine - INFO - Epoch(train) [14][2/2]  lr: 1.0000e-03  eta: 0:00:03  time: 0.1456  data_time: 0.0410  memory: 2507  loss: 1.8242  grad_norm: 2.7135
[2025-07-31 12:04:11]    07/31 12:04:11 - mmengine - INFO - Epoch(val) [14][2/2]    auc: 1.7040  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0270  data_time: 0.0099  time: 0.2668
[2025-07-31 12:04:12]    07/31 12:04:12 - mmengine - INFO - Epoch(train) [15][2/2]  lr: 1.0000e-03  eta: 0:00:03  time: 0.1455  data_time: 0.0408  memory: 2507  loss: 1.8057  grad_norm: 2.7135
[2025-07-31 12:04:12]    07/31 12:04:12 - mmengine - INFO - Saving checkpoint at 15 epochs
[2025-07-31 12:04:13]    07/31 12:04:13 - mmengine - INFO - Epoch(train) [16][2/2]  lr: 3.0000e-04  eta: 0:00:02  time: 0.1458  data_time: 0.0410  memory: 2507  loss: 1.7994  grad_norm: 2.5769
[2025-07-31 12:04:13]    07/31 12:04:13 - mmengine - INFO - Epoch(val) [16][2/2]    auc: 1.6341  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0270  data_time: 0.0093  time: 0.2618
[2025-07-31 12:04:13]    07/31 12:04:13 - mmengine - INFO - Epoch(train) [17][2/2]  lr: 3.0000e-04  eta: 0:00:02  time: 0.1457  data_time: 0.0407  memory: 2507  loss: 1.7768  grad_norm: 2.5769
[2025-07-31 12:04:14]    07/31 12:04:14 - mmengine - INFO - Epoch(train) [18][2/2]  lr: 3.0000e-04  eta: 0:00:02  time: 0.1452  data_time: 0.0407  memory: 2507  loss: 1.7555  grad_norm: 2.4231
[2025-07-31 12:04:14]    07/31 12:04:14 - mmengine - INFO - Epoch(val) [18][2/2]    auc: 1.5960  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0270  data_time: 0.0079  time: 0.2619
[2025-07-31 12:04:14]    07/31 12:04:14 - mmengine - INFO - Epoch(train) [19][2/2]  lr: 3.0000e-04  eta: 0:00:01  time: 0.1453  data_time: 0.0408  memory: 2507  loss: 1.7413  grad_norm: 2.4231
[2025-07-31 12:04:15]    07/31 12:04:15 - mmengine - INFO - Epoch(train) [20][2/2]  lr: 3.0000e-04  eta: 0:00:01  time: 0.1453  data_time: 0.0408  memory: 2507  loss: 1.7275  grad_norm: 2.2715
[2025-07-31 12:04:15]    07/31 12:04:15 - mmengine - INFO - Saving checkpoint at 20 epochs
[2025-07-31 12:04:16]    07/31 12:04:16 - mmengine - INFO - Epoch(val) [20][2/2]    auc: 1.5865  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0270  data_time: 0.0085  time: 0.2620
[2025-07-31 12:04:16]    07/31 12:04:16 - mmengine - INFO - Epoch(train) [21][2/2]  lr: 9.0000e-05  eta: 0:00:01  time: 0.1454  data_time: 0.0409  memory: 2507  loss: 1.7136  grad_norm: 2.2715
[2025-07-31 12:04:17]    07/31 12:04:17 - mmengine - INFO - Epoch(train) [22][2/2]  lr: 9.0000e-05  eta: 0:00:00  time: 0.1455  data_time: 0.0409  memory: 2507  loss: 1.7015  grad_norm: 2.1505
[2025-07-31 12:04:17]    07/31 12:04:17 - mmengine - INFO - Epoch(val) [22][2/2]    auc: 1.6262  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0270  data_time: 0.0110  time: 0.2572
[2025-07-31 12:04:18]    07/31 12:04:18 - mmengine - INFO - Epoch(train) [23][2/2]  lr: 9.0000e-05  eta: 0:00:00  time: 0.1456  data_time: 0.0409  memory: 2507  loss: 1.6966  grad_norm: 2.1505
[2025-07-31 12:04:18]    07/31 12:04:18 - mmengine - INFO - Epoch(train) [24][2/2]  lr: 9.0000e-05  eta: 0:00:00  time: 0.1453  data_time: 0.0406  memory: 2507  loss: 1.6680  grad_norm: 2.0347
[2025-07-31 12:04:18]    07/31 12:04:18 - mmengine - INFO - Epoch(val) [24][2/2]    auc: 1.6230  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0270  data_time: 0.0084  time: 0.2541
[2025-07-31 12:04:19]    07/31 12:04:19 - mmengine - INFO - Epoch(train) [25][2/2]  lr: 9.0000e-05  eta: 0:00:00  time: 0.1454  data_time: 0.0406  memory: 2507  loss: 1.6675  grad_norm: 1.9296
[2025-07-31 12:04:19]    07/31 12:04:19 - mmengine - INFO - Saving checkpoint at 25 epochs
[2025-07-31 12:04:20]    07/31 12:04:20 - mmengine - INFO - Epoch(val) [25][2/2]    auc: 1.5706  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0238  data_time: 0.0091  time: 0.2510
[2025-07-31 12:04:21] 
🎉 实验 exp1_2_batch_optimization 完成! 耗时: 0.5分钟
[2025-07-31 12:04:21] ✅ 实验 exp1_2_batch_optimization.py 成功完成
[2025-07-31 12:04:21] 📈 关键指标:
[2025-07-31 12:04:21]    ar@1: 0.0
[2025-07-31 12:04:21]    ar@10: 0.0
[2025-07-31 12:04:21]    loss: 1.6675
[2025-07-31 12:04:21]    auc: 1.5706
[2025-07-31 12:04:21]    ar@5: 0.0
[2025-07-31 12:04:21] 💾 实验结果已保存到: ../../../experiment_results/experiment_results_20250731_120259.json
[2025-07-31 12:04:21] 
============================================================
[2025-07-31 12:04:21] 📊 实验进度: 3/3 - exp2_1_boundary_optimization.py
[2025-07-31 12:04:21] ============================================================
[2025-07-31 12:04:21] 📝 实验描述: 实验2.1: 边界参数优化配置
目标: 优化boundary_ratio和num_samples参数，提高边界检测精度
预期效果: 显著改善AR@1/5/10指标，提高精确定位能力...
[2025-07-31 12:04:21] 🔧 关键参数: {'boundary_ratio': '0.3', 'num_samples': '64', 'lr': '0.0008', 'max_epochs': '35'}
[2025-07-31 12:04:21] 
🚀 开始实验: exp2_1_boundary_optimization
[2025-07-31 12:04:21]    配置文件: ../configs/exp2_1_boundary_optimization.py
[2025-07-31 12:04:21]    GPU设备: 0
[2025-07-31 12:04:21]    执行命令: python /home/<USER>/johnny_ws/mmaction2_ws/data_process/training/scripts/../../../mmaction2/tools/train.py /home/<USER>/johnny_ws/mmaction2_ws/data_process/training/scripts/../configs/exp2_1_boundary_optimization.py
[2025-07-31 12:04:21] ============================================================
[2025-07-31 12:04:24]    '显著改善AR@1/5/10指标',
[2025-07-31 12:04:24]    '保持或提高AUC',
[2025-07-31 12:04:24]    'AR@1 > 0.02',
[2025-07-31 12:04:24]    'AR@5 > 0.08',
[2025-07-31 12:04:24]    'AR@10 > 0.15',
[2025-07-31 12:04:24]    'AR@100 > 0.04',
[2025-07-31 12:04:24]    'AUC > 2.0',
[2025-07-31 12:04:24]    log_processor = dict(by_epoch=True, type='LogProcessor', window_size=20)
[2025-07-31 12:04:24]    max_epochs = 35
[2025-07-31 12:04:24]    by_epoch=True,
[2025-07-31 12:04:24]    metric_type='AR@AN',
[2025-07-31 12:04:24]    max_epochs=35, type='EpochBasedTrainLoop', val_begin=1, val_interval=2)
[2025-07-31 12:04:24]    metric_type='AR@AN',
[2025-07-31 12:04:29]    before_train_epoch:
[2025-07-31 12:04:29]    after_train_epoch:
[2025-07-31 12:04:29]    before_val_epoch:
[2025-07-31 12:04:29]    after_val_epoch:
[2025-07-31 12:04:29]    before_test_epoch:
[2025-07-31 12:04:29]    after_test_epoch:
[2025-07-31 12:04:31]    07/31 12:04:31 - mmengine - INFO - Epoch(train)  [1][2/2]  lr: 8.0000e-04  eta: 0:00:33  time: 0.4907  data_time: 0.0942  memory: 8195  grad_norm: 0.5751  loss: 2.1048
[2025-07-31 12:04:31]    07/31 12:04:31 - mmengine - INFO - Epoch(train)  [2][2/2]  lr: 8.0000e-04  eta: 0:00:23  time: 0.3597  data_time: 0.0669  memory: 8195  grad_norm: 2.4368  loss: 2.1041
[2025-07-31 12:04:33]    07/31 12:04:33 - mmengine - INFO - Epoch(val) [2][2/2]    auc: 2.0476  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0286  data_time: 0.0597  time: 0.5592
[2025-07-31 12:04:33]    07/31 12:04:33 - mmengine - INFO - The best checkpoint with 2.0476 auc at 2 epoch is saved to best_auc_epoch_2.pth.
[2025-07-31 12:04:34]    07/31 12:04:34 - mmengine - INFO - Epoch(train)  [3][2/2]  lr: 8.0000e-04  eta: 0:00:20  time: 0.3157  data_time: 0.0584  memory: 8195  grad_norm: 2.0714  loss: 2.0413
[2025-07-31 12:04:34]    07/31 12:04:34 - mmengine - INFO - Epoch(train)  [4][2/2]  lr: 8.0000e-04  eta: 0:00:18  time: 0.2936  data_time: 0.0533  memory: 8195  grad_norm: 2.8417  loss: 1.9827
[2025-07-31 12:04:35]    07/31 12:04:35 - mmengine - INFO - Epoch(val) [4][2/2]    auc: 2.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0286  data_time: 0.0100  time: 0.5081
[2025-07-31 12:04:36]    07/31 12:04:36 - mmengine - INFO - Epoch(train)  [5][2/2]  lr: 8.0000e-04  eta: 0:00:16  time: 0.2807  data_time: 0.0509  memory: 8195  grad_norm: 3.3475  loss: 1.9481
[2025-07-31 12:04:36]    07/31 12:04:36 - mmengine - INFO - Saving checkpoint at 5 epochs
[2025-07-31 12:04:37]    07/31 12:04:37 - mmengine - INFO - Epoch(train)  [6][2/2]  lr: 8.0000e-04  eta: 0:00:15  time: 0.2725  data_time: 0.0497  memory: 8195  grad_norm: 3.2128  loss: 1.9174
[2025-07-31 12:04:38]    07/31 12:04:38 - mmengine - INFO - Epoch(val) [6][2/2]    auc: 2.0071  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0302  data_time: 0.0092  time: 0.5046
[2025-07-31 12:04:39]    07/31 12:04:39 - mmengine - INFO - Epoch(train)  [7][2/2]  lr: 8.0000e-04  eta: 0:00:14  time: 0.2658  data_time: 0.0481  memory: 8195  grad_norm: 2.8608  loss: 1.8914
[2025-07-31 12:04:39]    07/31 12:04:39 - mmengine - INFO - Epoch(train)  [8][2/2]  lr: 8.0000e-04  eta: 0:00:14  time: 0.2615  data_time: 0.0474  memory: 8195  grad_norm: 2.6783  loss: 1.8614
[2025-07-31 12:04:40]    07/31 12:04:40 - mmengine - INFO - Epoch(val) [8][2/2]    auc: 1.9151  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0302  data_time: 0.0100  time: 0.5076
[2025-07-31 12:04:40]    07/31 12:04:40 - mmengine - INFO - Epoch(train)  [9][2/2]  lr: 8.0000e-04  eta: 0:00:13  time: 0.2581  data_time: 0.0465  memory: 8195  grad_norm: 2.4768  loss: 1.8342
[2025-07-31 12:04:41]    07/31 12:04:41 - mmengine - INFO - Epoch(train) [10][2/2]  lr: 8.0000e-04  eta: 0:00:12  time: 0.2552  data_time: 0.0458  memory: 8195  grad_norm: 2.3254  loss: 1.8136
[2025-07-31 12:04:41]    07/31 12:04:41 - mmengine - INFO - Saving checkpoint at 10 epochs
[2025-07-31 12:04:42]    07/31 12:04:42 - mmengine - INFO - Epoch(val) [10][2/2]    auc: 0.2643  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0048  data_time: 0.0089  time: 0.3658
[2025-07-31 12:04:43]    07/31 12:04:43 - mmengine - INFO - Epoch(train) [11][2/2]  lr: 8.0000e-04  eta: 0:00:12  time: 0.2295  data_time: 0.0404  memory: 8195  grad_norm: 2.4062  loss: 1.7578
[2025-07-31 12:04:43]    07/31 12:04:43 - mmengine - INFO - Epoch(train) [12][2/2]  lr: 8.0000e-04  eta: 0:00:11  time: 0.2294  data_time: 0.0403  memory: 8195  grad_norm: 2.1081  loss: 1.7001
[2025-07-31 12:04:44]    07/31 12:04:44 - mmengine - INFO - Epoch(val) [12][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0094  time: 0.2016
[2025-07-31 12:04:44]    07/31 12:04:44 - mmengine - INFO - Epoch(train) [13][2/2]  lr: 8.0000e-04  eta: 0:00:10  time: 0.2294  data_time: 0.0400  memory: 8195  grad_norm: 2.0865  loss: 1.6576
[2025-07-31 12:04:45]    07/31 12:04:45 - mmengine - INFO - Epoch(train) [14][2/2]  lr: 8.0000e-04  eta: 0:00:10  time: 0.2301  data_time: 0.0403  memory: 8195  grad_norm: 1.7189  loss: 1.6140
[2025-07-31 12:04:45]    07/31 12:04:45 - mmengine - INFO - Epoch(val) [14][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0084  time: 0.0837
[2025-07-31 12:04:45]    07/31 12:04:45 - mmengine - INFO - Epoch(train) [15][2/2]  lr: 8.0000e-04  eta: 0:00:09  time: 0.2303  data_time: 0.0404  memory: 8195  grad_norm: 2.0365  loss: 1.6054
[2025-07-31 12:04:45]    07/31 12:04:45 - mmengine - INFO - Saving checkpoint at 15 epochs
[2025-07-31 12:04:47]    07/31 12:04:47 - mmengine - INFO - Epoch(train) [16][2/2]  lr: 4.0000e-04  eta: 0:00:09  time: 0.2306  data_time: 0.0402  memory: 8195  grad_norm: 2.1748  loss: 1.5730
[2025-07-31 12:04:47]    07/31 12:04:47 - mmengine - INFO - Epoch(val) [16][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0086  time: 0.0848
[2025-07-31 12:04:47]    07/31 12:04:47 - mmengine - INFO - Epoch(train) [17][2/2]  lr: 4.0000e-04  eta: 0:00:08  time: 0.2313  data_time: 0.0402  memory: 8195  grad_norm: 2.3551  loss: 1.5426
[2025-07-31 12:04:48]    07/31 12:04:48 - mmengine - INFO - Epoch(train) [18][2/2]  lr: 4.0000e-04  eta: 0:00:08  time: 0.2316  data_time: 0.0402  memory: 8195  grad_norm: 2.4135  loss: 1.5136
[2025-07-31 12:04:48]    07/31 12:04:48 - mmengine - INFO - Epoch(val) [18][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0107  time: 0.0782
[2025-07-31 12:04:48]    07/31 12:04:48 - mmengine - INFO - Epoch(train) [19][2/2]  lr: 4.0000e-04  eta: 0:00:07  time: 0.2317  data_time: 0.0401  memory: 8195  grad_norm: 2.4767  loss: 1.4836
[2025-07-31 12:04:49]    07/31 12:04:49 - mmengine - INFO - Epoch(train) [20][2/2]  lr: 4.0000e-04  eta: 0:00:07  time: 0.2318  data_time: 0.0402  memory: 8195  grad_norm: 2.6331  loss: 1.4499
[2025-07-31 12:04:49]    07/31 12:04:49 - mmengine - INFO - Saving checkpoint at 20 epochs
[2025-07-31 12:04:50]    07/31 12:04:50 - mmengine - INFO - Epoch(val) [20][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0108  time: 0.1067
[2025-07-31 12:04:50]    07/31 12:04:50 - mmengine - INFO - Epoch(train) [21][2/2]  lr: 4.0000e-04  eta: 0:00:06  time: 0.2314  data_time: 0.0401  memory: 8195  grad_norm: 2.6578  loss: 1.4160
[2025-07-31 12:04:51]    07/31 12:04:51 - mmengine - INFO - Epoch(train) [22][2/2]  lr: 4.0000e-04  eta: 0:00:06  time: 0.2315  data_time: 0.0404  memory: 8195  grad_norm: 2.7777  loss: 1.3855
[2025-07-31 12:04:51]    07/31 12:04:51 - mmengine - INFO - Epoch(val) [22][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0084  time: 0.1064
[2025-07-31 12:04:52]    07/31 12:04:52 - mmengine - INFO - Epoch(train) [23][2/2]  lr: 4.0000e-04  eta: 0:00:05  time: 0.2316  data_time: 0.0405  memory: 8195  grad_norm: 2.8727  loss: 1.3545
[2025-07-31 12:04:52]    07/31 12:04:52 - mmengine - INFO - Epoch(train) [24][2/2]  lr: 4.0000e-04  eta: 0:00:05  time: 0.2314  data_time: 0.0405  memory: 8195  grad_norm: 2.9524  loss: 1.3291
[2025-07-31 12:04:52]    07/31 12:04:52 - mmengine - INFO - Epoch(val) [24][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0091  time: 0.0808
[2025-07-31 12:04:53]    07/31 12:04:53 - mmengine - INFO - Epoch(train) [25][2/2]  lr: 4.0000e-04  eta: 0:00:04  time: 0.2314  data_time: 0.0404  memory: 8195  grad_norm: 2.3713  loss: 1.2630
[2025-07-31 12:04:53]    07/31 12:04:53 - mmengine - INFO - Saving checkpoint at 25 epochs
[2025-07-31 12:04:54]    07/31 12:04:54 - mmengine - INFO - Epoch(train) [26][2/2]  lr: 2.0000e-04  eta: 0:00:04  time: 0.2312  data_time: 0.0404  memory: 8195  grad_norm: 2.2853  loss: 1.2336
[2025-07-31 12:04:54]    07/31 12:04:54 - mmengine - INFO - Epoch(val) [26][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0105  time: 0.0870
[2025-07-31 12:04:55]    07/31 12:04:55 - mmengine - INFO - Epoch(train) [27][2/2]  lr: 2.0000e-04  eta: 0:00:03  time: 0.2308  data_time: 0.0405  memory: 8195  grad_norm: 2.1904  loss: 1.1992
[2025-07-31 12:04:55]    07/31 12:04:55 - mmengine - INFO - Epoch(train) [28][2/2]  lr: 2.0000e-04  eta: 0:00:03  time: 0.2304  data_time: 0.0405  memory: 8195  grad_norm: 2.2535  loss: 1.1740
[2025-07-31 12:04:55]    07/31 12:04:55 - mmengine - INFO - Epoch(val) [28][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0088  time: 0.1198
[2025-07-31 12:04:56]    07/31 12:04:56 - mmengine - INFO - Epoch(train) [29][2/2]  lr: 2.0000e-04  eta: 0:00:02  time: 0.2301  data_time: 0.0406  memory: 8195  grad_norm: 2.3341  loss: 1.1508
[2025-07-31 12:04:56]    07/31 12:04:56 - mmengine - INFO - Epoch(train) [30][2/2]  lr: 2.0000e-04  eta: 0:00:02  time: 0.2295  data_time: 0.0403  memory: 8195  grad_norm: 2.2528  loss: 1.1279
[2025-07-31 12:04:56]    07/31 12:04:56 - mmengine - INFO - Saving checkpoint at 30 epochs
[2025-07-31 12:04:57]    07/31 12:04:57 - mmengine - INFO - Epoch(val) [30][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0103  time: 0.1154
[2025-07-31 12:04:58]    07/31 12:04:58 - mmengine - INFO - Epoch(train) [31][2/2]  lr: 1.0000e-04  eta: 0:00:01  time: 0.2304  data_time: 0.0407  memory: 8195  grad_norm: 2.2455  loss: 1.1090
[2025-07-31 12:04:58]    07/31 12:04:58 - mmengine - INFO - Epoch(train) [32][2/2]  lr: 1.0000e-04  eta: 0:00:01  time: 0.2306  data_time: 0.0406  memory: 8195  grad_norm: 2.1740  loss: 1.0904
[2025-07-31 12:04:58]    07/31 12:04:58 - mmengine - INFO - Epoch(val) [32][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0083  time: 0.0714
[2025-07-31 12:04:59]    07/31 12:04:59 - mmengine - INFO - Epoch(train) [33][2/2]  lr: 1.0000e-04  eta: 0:00:00  time: 0.2309  data_time: 0.0406  memory: 8195  grad_norm: 2.0949  loss: 1.0751
[2025-07-31 12:04:59]    07/31 12:04:59 - mmengine - INFO - Epoch(train) [34][2/2]  lr: 1.0000e-04  eta: 0:00:00  time: 0.2310  data_time: 0.0407  memory: 8195  grad_norm: 1.9893  loss: 1.0624
[2025-07-31 12:05:00]    07/31 12:05:00 - mmengine - INFO - Epoch(val) [34][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0092  time: 0.0817
[2025-07-31 12:05:00]    07/31 12:05:00 - mmengine - INFO - Epoch(train) [35][2/2]  lr: 1.0000e-04  eta: 0:00:00  time: 0.2309  data_time: 0.0405  memory: 8195  grad_norm: 1.9174  loss: 1.0590
[2025-07-31 12:05:00]    07/31 12:05:00 - mmengine - INFO - Saving checkpoint at 35 epochs
[2025-07-31 12:05:01]    07/31 12:05:01 - mmengine - INFO - Epoch(val) [35][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0081  time: 0.0871
[2025-07-31 12:05:02] 
🎉 实验 exp2_1_boundary_optimization 完成! 耗时: 0.7分钟
[2025-07-31 12:05:02] ✅ 实验 exp2_1_boundary_optimization.py 成功完成
[2025-07-31 12:05:02] 📈 关键指标:
[2025-07-31 12:05:02]    ar@1: 0.0
[2025-07-31 12:05:02]    ar@10: 0.0
[2025-07-31 12:05:02]    loss: 1.059
[2025-07-31 12:05:02]    auc: 0.0
[2025-07-31 12:05:02]    ar@5: 0.0
[2025-07-31 12:05:02] 💾 实验结果已保存到: ../../../experiment_results/experiment_results_20250731_120259.json
[2025-07-31 12:05:02] 
================================================================================
[2025-07-31 12:05:02] 📊 实验汇总报告
[2025-07-31 12:05:02] ================================================================================
[2025-07-31 12:05:02] ⏱️  总耗时: 2.1 分钟
[2025-07-31 12:05:02] ✅ 成功实验: 3
[2025-07-31 12:05:02] ❌ 失败实验: 0
[2025-07-31 12:05:02] 📁 详细日志: ../../../experiment_results/experiment_log_20250731_120259.txt
[2025-07-31 12:05:02] 📄 结果文件: ../../../experiment_results/experiment_results_20250731_120259.json
[2025-07-31 12:05:02] 
🏆 成功实验指标对比:
[2025-07-31 12:05:02] --------------------------------------------------------------------------------
[2025-07-31 12:05:02] 实验名称                      AUC      AR@1     AR@5     AR@10    耗时(分)   
[2025-07-31 12:05:02] --------------------------------------------------------------------------------
[2025-07-31 12:05:02] exp1_1_data_augmentation  0.0000   0.0000   0.0000   0.0000   0.9     
[2025-07-31 12:05:02] exp1_2_batch_optimizatio  1.5706   0.0000   0.0000   0.0000   0.5     
[2025-07-31 12:05:02] exp2_1_boundary_optimiza  0.0000   0.0000   0.0000   0.0000   0.7     
[2025-07-31 12:05:02] 
🥇 最佳实验:
[2025-07-31 12:05:02]    最高AUC: exp1_2_batch_optimization (AUC: 1.5706)
[2025-07-31 12:05:02]    最高AR@1: exp1_1_data_augmentation (AR@1: 0.0000)
[2025-07-31 12:05:02] 
💡 后续建议:
[2025-07-31 12:05:02]    1. 分析最佳实验的参数设置
[2025-07-31 12:05:02]    2. 基于最佳结果进行进一步的参数微调
[2025-07-31 12:05:02]    3. 考虑组合多个有效的优化策略
[2025-07-31 12:05:02] ================================================================================
