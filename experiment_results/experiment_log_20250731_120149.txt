[2025-07-31 12:01:49] 🔍 检查训练环境...
[2025-07-31 12:01:50] ✅ CUDA可用: NVIDIA RTX A5000
[2025-07-31 12:01:50]    GPU内存: 23.6GB
[2025-07-31 12:01:50] ✅ MMAction2已安装: 1.2.0
[2025-07-31 12:01:50] 
📁 检查数据文件...
[2025-07-31 12:01:50] ✅ 标注文件: ../../../data/MultiClassTAD/multiclass_tad_train.json
[2025-07-31 12:01:50] ✅ 标注文件: ../../../data/MultiClassTAD/multiclass_tad_val.json
[2025-07-31 12:01:50] ✅ 特征目录: ../../../data/MultiClassTAD/features_slowonly (10 个CSV文件)
[2025-07-31 12:01:50] 
🚀 开始实验: exp1_1_data_augmentation
[2025-07-31 12:01:50]    配置文件: ../configs/exp1_1_data_augmentation.py
[2025-07-31 12:01:50]    GPU设备: 0
[2025-07-31 12:01:50]    执行命令: python /home/<USER>/johnny_ws/mmaction2_ws/data_process/training/scripts/../../../mmaction2/tools/train.py /home/<USER>/johnny_ws/mmaction2_ws/data_process/training/scripts/../configs/exp1_1_data_augmentation.py
[2025-07-31 12:01:50] ============================================================
[2025-07-31 12:01:53]    '改善AR@1/5/10指标',
[2025-07-31 12:01:53]    'AR@1 > 0.01',
[2025-07-31 12:01:53]    'AR@5 > 0.05',
[2025-07-31 12:01:53]    'AR@10 > 0.10',
[2025-07-31 12:01:53]    'AUC保持在2.0以上',
[2025-07-31 12:01:53]    log_processor = dict(by_epoch=True, type='LogProcessor', window_size=20)
[2025-07-31 12:01:53]    max_epochs = 30
[2025-07-31 12:01:53]    by_epoch=True,
[2025-07-31 12:01:53]    metric_type='AR@AN',
[2025-07-31 12:01:53]    max_epochs=30, type='EpochBasedTrainLoop', val_begin=1, val_interval=2)
[2025-07-31 12:01:53]    metric_type='AR@AN',
[2025-07-31 12:01:56]    before_train_epoch:
[2025-07-31 12:01:56]    after_train_epoch:
[2025-07-31 12:01:56]    before_val_epoch:
[2025-07-31 12:01:56]    after_val_epoch:
[2025-07-31 12:01:56]    before_test_epoch:
[2025-07-31 12:01:56]    after_test_epoch:
[2025-07-31 12:01:58]    07/31 12:01:58 - mmengine - INFO - Epoch(train)  [1][1/1]  lr: 5.0000e-04  eta: 0:00:47  time: 1.6283  data_time: 0.6601  memory: 8060  grad_norm: 0.3118  loss: 2.0931
[2025-07-31 12:01:59]    07/31 12:01:59 - mmengine - INFO - Epoch(train)  [2][1/1]  lr: 5.0000e-04  eta: 0:00:35  time: 1.2735  data_time: 0.5005  memory: 8110  grad_norm: 0.3220  loss: 2.0917
[2025-07-31 12:02:00]    07/31 12:02:00 - mmengine - INFO - Epoch(val) [2][2/2]    auc: 1.6611  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0270  data_time: 0.0583  time: 0.3183
[2025-07-31 12:02:00]    07/31 12:02:00 - mmengine - INFO - The best checkpoint with 1.6611 auc at 2 epoch is saved to best_auc_epoch_2.pth.
[2025-07-31 12:02:01]    07/31 12:02:01 - mmengine - INFO - Epoch(train)  [3][1/1]  lr: 5.0000e-04  eta: 0:00:29  time: 1.1039  data_time: 0.3926  memory: 8110  grad_norm: 0.3969  loss: 2.0783
[2025-07-31 12:02:02]    07/31 12:02:02 - mmengine - INFO - Epoch(train)  [4][1/1]  lr: 5.0000e-04  eta: 0:00:28  time: 1.1031  data_time: 0.4239  memory: 8110  grad_norm: 0.5700  loss: 2.0404
[2025-07-31 12:02:03]    07/31 12:02:03 - mmengine - INFO - Epoch(val) [4][2/2]    auc: 1.8770  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0270  data_time: 0.0102  time: 0.2637
[2025-07-31 12:02:03]    07/31 12:02:03 - mmengine - INFO - The previous best checkpoint /home/<USER>/johnny_ws/mmaction2_ws/work_dirs/bmn_exp1_1_data_aug/best_auc_epoch_2.pth is removed
[2025-07-31 12:02:03]    07/31 12:02:03 - mmengine - INFO - The best checkpoint with 1.8770 auc at 4 epoch is saved to best_auc_epoch_4.pth.
[2025-07-31 12:02:04]    07/31 12:02:04 - mmengine - INFO - Epoch(train)  [5][1/1]  lr: 5.0000e-04  eta: 0:00:27  time: 1.1002  data_time: 0.4402  memory: 8110  grad_norm: 1.5843  loss: 2.0110
[2025-07-31 12:02:04]    07/31 12:02:04 - mmengine - INFO - Saving checkpoint at 5 epochs
[2025-07-31 12:02:06]    07/31 12:02:06 - mmengine - INFO - Epoch(train)  [6][1/1]  lr: 5.0000e-04  eta: 0:00:25  time: 1.0433  data_time: 0.3950  memory: 8110  grad_norm: 1.4002  loss: 1.9698
[2025-07-31 12:02:06]    07/31 12:02:06 - mmengine - INFO - Epoch(val) [6][2/2]    auc: 1.8468  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0270  data_time: 0.0097  time: 0.2659
[2025-07-31 12:02:07]    07/31 12:02:07 - mmengine - INFO - Epoch(train)  [7][1/1]  lr: 5.0000e-04  eta: 0:00:24  time: 1.0542  data_time: 0.4148  memory: 8110  grad_norm: 1.9596  loss: 1.9475
[2025-07-31 12:02:08]    07/31 12:02:08 - mmengine - INFO - Epoch(train)  [8][1/1]  lr: 5.0000e-04  eta: 0:00:23  time: 1.0588  data_time: 0.4271  memory: 8110  grad_norm: 2.1136  loss: 1.9231
[2025-07-31 12:02:09]    07/31 12:02:09 - mmengine - INFO - Epoch(val) [8][2/2]    auc: 1.7833  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0270  data_time: 0.0099  time: 0.2658
[2025-07-31 12:02:10]    07/31 12:02:10 - mmengine - INFO - Epoch(train)  [9][1/1]  lr: 5.0000e-04  eta: 0:00:21  time: 1.0425  data_time: 0.4154  memory: 8110  grad_norm: 1.9779  loss: 1.9019
[2025-07-31 12:02:11]    07/31 12:02:11 - mmengine - INFO - Epoch(train) [10][1/1]  lr: 5.0000e-04  eta: 0:00:21  time: 1.0508  data_time: 0.4266  memory: 8110  grad_norm: 2.1273  loss: 1.8844
[2025-07-31 12:02:11]    07/31 12:02:11 - mmengine - INFO - Saving checkpoint at 10 epochs
[2025-07-31 12:02:12]    07/31 12:02:12 - mmengine - INFO - Epoch(val) [10][2/2]    auc: 1.7421  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0270  data_time: 0.0116  time: 0.2775
[2025-07-31 12:02:13]    07/31 12:02:13 - mmengine - INFO - Epoch(train) [11][1/1]  lr: 5.0000e-04  eta: 0:00:20  time: 1.0746  data_time: 0.4538  memory: 8110  grad_norm: 2.1356  loss: 1.8705
[2025-07-31 12:02:14]    07/31 12:02:14 - mmengine - INFO - Epoch(train) [12][1/1]  lr: 5.0000e-04  eta: 0:00:19  time: 1.0775  data_time: 0.4594  memory: 8110  grad_norm: 1.9908  loss: 1.8550
[2025-07-31 12:02:15]    07/31 12:02:15 - mmengine - INFO - Epoch(val) [12][2/2]    auc: 1.6500  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0270  data_time: 0.0104  time: 0.2696
[2025-07-31 12:02:16]    07/31 12:02:16 - mmengine - INFO - Epoch(train) [13][1/1]  lr: 5.0000e-04  eta: 0:00:18  time: 1.0663  data_time: 0.4509  memory: 8110  grad_norm: 1.9055  loss: 1.8414
[2025-07-31 12:02:17]    07/31 12:02:17 - mmengine - INFO - Epoch(train) [14][1/1]  lr: 5.0000e-04  eta: 0:00:16  time: 1.0564  data_time: 0.4431  memory: 8110  grad_norm: 1.8745  loss: 1.8277
[2025-07-31 12:02:17]    07/31 12:02:17 - mmengine - INFO - Epoch(val) [14][2/2]    auc: 1.6246  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0270  data_time: 0.0090  time: 0.2567
[2025-07-31 12:02:18]    07/31 12:02:18 - mmengine - INFO - Epoch(train) [15][1/1]  lr: 5.0000e-04  eta: 0:00:15  time: 1.0355  data_time: 0.4242  memory: 8110  grad_norm: 1.8153  loss: 1.8140
[2025-07-31 12:02:18]    07/31 12:02:18 - mmengine - INFO - Saving checkpoint at 15 epochs
[2025-07-31 12:02:20]    07/31 12:02:20 - mmengine - INFO - Epoch(train) [16][1/1]  lr: 5.0000e-04  eta: 0:00:14  time: 1.0290  data_time: 0.4191  memory: 8110  grad_norm: 1.7842  loss: 1.8031
[2025-07-31 12:02:20]    07/31 12:02:20 - mmengine - INFO - Epoch(val) [16][2/2]    auc: 0.6151  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0079  data_time: 0.0105  time: 0.2198
[2025-07-31 12:02:21]    07/31 12:02:21 - mmengine - INFO - Epoch(train) [17][1/1]  lr: 5.0000e-04  eta: 0:00:13  time: 1.0456  data_time: 0.4368  memory: 8110  grad_norm: 1.7743  loss: 1.7904
[2025-07-31 12:02:22]    07/31 12:02:22 - mmengine - INFO - Epoch(train) [18][1/1]  lr: 5.0000e-04  eta: 0:00:12  time: 1.0383  data_time: 0.4311  memory: 8110  grad_norm: 1.7473  loss: 1.7777
[2025-07-31 12:02:23]    07/31 12:02:23 - mmengine - INFO - Epoch(val) [18][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0107  time: 0.1587
[2025-07-31 12:02:23]    07/31 12:02:23 - mmengine - INFO - Epoch(train) [19][1/1]  lr: 5.0000e-04  eta: 0:00:11  time: 1.0228  data_time: 0.4161  memory: 8110  grad_norm: 1.7266  loss: 1.7634
[2025-07-31 12:02:24]    07/31 12:02:24 - mmengine - INFO - Epoch(train) [20][1/1]  lr: 5.0000e-04  eta: 0:00:10  time: 1.0178  data_time: 0.4120  memory: 8110  grad_norm: 1.7351  loss: 1.7488
[2025-07-31 12:02:24]    07/31 12:02:24 - mmengine - INFO - Saving checkpoint at 20 epochs
[2025-07-31 12:02:25]    07/31 12:02:25 - mmengine - INFO - Epoch(val) [20][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0091  time: 0.1120
[2025-07-31 12:02:26]    07/31 12:02:26 - mmengine - INFO - Epoch(train) [21][1/1]  lr: 2.5000e-04  eta: 0:00:09  time: 0.9731  data_time: 0.3862  memory: 8110  grad_norm: 1.8119  loss: 1.7159
[2025-07-31 12:02:27]    07/31 12:02:27 - mmengine - INFO - Epoch(train) [22][1/1]  lr: 2.5000e-04  eta: 0:00:08  time: 0.9736  data_time: 0.3859  memory: 8110  grad_norm: 1.8464  loss: 1.6819
[2025-07-31 12:02:27]    07/31 12:02:27 - mmengine - INFO - Epoch(val) [22][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0086  time: 0.0799
[2025-07-31 12:02:28]    07/31 12:02:28 - mmengine - INFO - Epoch(train) [23][1/1]  lr: 2.5000e-04  eta: 0:00:06  time: 0.9814  data_time: 0.3938  memory: 8110  grad_norm: 1.8930  loss: 1.6476
[2025-07-31 12:02:29]    07/31 12:02:29 - mmengine - INFO - Epoch(train) [24][1/1]  lr: 2.5000e-04  eta: 0:00:06  time: 0.9811  data_time: 0.3932  memory: 8110  grad_norm: 1.9136  loss: 1.6197
[2025-07-31 12:02:29]    07/31 12:02:29 - mmengine - INFO - Epoch(val) [24][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0103  time: 0.0576
[2025-07-31 12:02:30]    07/31 12:02:30 - mmengine - INFO - Epoch(train) [25][1/1]  lr: 2.5000e-04  eta: 0:00:05  time: 0.9820  data_time: 0.3943  memory: 8110  grad_norm: 1.6769  loss: 1.5944
[2025-07-31 12:02:30]    07/31 12:02:30 - mmengine - INFO - Saving checkpoint at 25 epochs
[2025-07-31 12:02:31]    07/31 12:02:31 - mmengine - INFO - Epoch(train) [26][1/1]  lr: 1.2500e-04  eta: 0:00:04  time: 0.9900  data_time: 0.4025  memory: 8110  grad_norm: 1.6888  loss: 1.5747
[2025-07-31 12:02:32]    07/31 12:02:32 - mmengine - INFO - Epoch(val) [26][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0110  time: 0.0629
[2025-07-31 12:02:33]    07/31 12:02:33 - mmengine - INFO - Epoch(train) [27][1/1]  lr: 1.2500e-04  eta: 0:00:03  time: 1.0076  data_time: 0.4205  memory: 8110  grad_norm: 1.4539  loss: 1.5537
[2025-07-31 12:02:34]    07/31 12:02:34 - mmengine - INFO - Epoch(train) [28][1/1]  lr: 1.2500e-04  eta: 0:00:02  time: 0.9895  data_time: 0.4025  memory: 8110  grad_norm: 1.3328  loss: 1.5328
[2025-07-31 12:02:34]    07/31 12:02:34 - mmengine - INFO - Epoch(val) [28][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0080  time: 0.0729
[2025-07-31 12:02:35]    07/31 12:02:35 - mmengine - INFO - Epoch(train) [29][1/1]  lr: 1.2500e-04  eta: 0:00:00  time: 0.9800  data_time: 0.3939  memory: 8110  grad_norm: 1.3237  loss: 1.5120
[2025-07-31 12:02:36]    07/31 12:02:36 - mmengine - INFO - Epoch(train) [30][1/1]  lr: 1.2500e-04  eta: 0:00:00  time: 0.9699  data_time: 0.3842  memory: 8110  grad_norm: 1.2001  loss: 1.4927
[2025-07-31 12:02:36]    07/31 12:02:36 - mmengine - INFO - Saving checkpoint at 30 epochs
[2025-07-31 12:02:37]    07/31 12:02:37 - mmengine - INFO - Epoch(val) [30][2/2]    auc: 0.0000  AR@1: 0.0000  AR@5: 0.0000  AR@10: 0.0000  AR@100: 0.0000  data_time: 0.0083  time: 0.0611
[2025-07-31 12:02:37] 
🎉 实验 exp1_1_data_augmentation 完成! 耗时: 0.8分钟
