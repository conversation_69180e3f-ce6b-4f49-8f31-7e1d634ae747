#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BMN模型优化实验管理器
用于管理和监控多个实验的运行
"""

import os
import json
import pandas as pd
from pathlib import Path
import argparse

class ExperimentManager:
    def __init__(self, base_dir=None):
        if base_dir is None:
            self.base_dir = Path(__file__).resolve().parents[1]
        else:
            self.base_dir = Path(base_dir)
        
        self.work_dirs_base = self.base_dir / 'work_dirs'
        self.configs_dir = self.base_dir / 'data_process' / 'training' / 'configs'
    
    def list_experiments(self):
        """列出所有可用的实验配置"""
        experiments = []
        
        for config_file in self.configs_dir.glob('exp*.py'):
            config_name = config_file.stem
            
            # 尝试读取配置信息
            config_info = self._extract_config_info(config_file)
            
            experiments.append({
                'config_file': config_file.name,
                'config_name': config_name,
                'experiment': config_info.get('experiment', 'Unknown'),
                'changes': config_info.get('changes', []),
                'expected_improvements': config_info.get('expected_improvements', []),
                'success_criteria': config_info.get('success_criteria', [])
            })
        
        return experiments
    
    def _extract_config_info(self, config_file):
        """从配置文件中提取实验信息"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找config_info字典
            if 'config_info = {' in content:
                start = content.find('config_info = {')
                # 简单的字典解析（假设格式规范）
                lines = content[start:].split('\n')
                info_lines = []
                brace_count = 0
                for line in lines:
                    info_lines.append(line)
                    brace_count += line.count('{') - line.count('}')
                    if brace_count == 0 and '}' in line:
                        break
                
                info_str = '\n'.join(info_lines)
                # 使用exec执行字典定义
                local_vars = {}
                exec(info_str, {}, local_vars)
                return local_vars.get('config_info', {})
        except Exception as e:
            print(f"警告: 无法解析配置文件 {config_file}: {e}")
        
        return {}
    
    def get_experiment_results(self, experiment_name=None):
        """获取实验结果"""
        results = []
        
        if experiment_name:
            work_dirs = [self.work_dirs_base / f"bmn_{experiment_name}"]
        else:
            work_dirs = [d for d in self.work_dirs_base.glob('bmn_exp*') if d.is_dir()]
        
        for work_dir in work_dirs:
            if not work_dir.exists():
                continue
            
            result = {
                'experiment': work_dir.name,
                'work_dir': str(work_dir),
                'status': 'unknown',
                'best_auc': None,
                'best_ar_1': None,
                'best_ar_5': None,
                'best_ar_10': None,
                'best_ar_100': None,
                'training_time': None,
                'best_epoch': None
            }
            
            # 检查训练状态
            log_files = list(work_dir.glob('*.log'))
            if log_files:
                latest_log = max(log_files, key=lambda x: x.stat().st_mtime)
                result['status'] = self._check_training_status(latest_log)
                
                # 解析训练结果
                metrics = self._parse_training_log(latest_log)
                result.update(metrics)
            
            # 检查是否有最佳模型
            best_models = list(work_dir.glob('best_*.pth'))
            if best_models:
                result['has_best_model'] = True
                # 从文件名中提取epoch信息
                for model in best_models:
                    if 'epoch' in model.name:
                        try:
                            epoch = int(model.name.split('epoch_')[1].split('.')[0])
                            result['best_epoch'] = epoch
                        except:
                            pass
            else:
                result['has_best_model'] = False
            
            results.append(result)
        
        return results
    
    def _check_training_status(self, log_file):
        """检查训练状态"""
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if '训练完成!' in content or 'Training completed' in content:
                return 'completed'
            elif 'ERROR' in content or 'Exception' in content:
                return 'failed'
            elif 'Epoch(' in content:
                return 'running'
            else:
                return 'unknown'
        except Exception:
            return 'unknown'
    
    def _parse_training_log(self, log_file):
        """解析训练日志获取指标"""
        metrics = {
            'best_auc': None,
            'best_ar_1': None,
            'best_ar_5': None,
            'best_ar_10': None,
            'best_ar_100': None
        }
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            best_auc = 0
            for line in lines:
                if 'auc:' in line and 'AR@' in line:
                    # 解析指标行
                    parts = line.split()
                    for i, part in enumerate(parts):
                        if part == 'auc:':
                            try:
                                auc = float(parts[i+1])
                                if auc > best_auc:
                                    best_auc = auc
                                    metrics['best_auc'] = auc
                                    
                                    # 解析AR指标
                                    for j in range(i+2, len(parts)):
                                        if parts[j].startswith('AR@'):
                                            ar_type = parts[j].rstrip(':')
                                            if j+1 < len(parts):
                                                ar_value = float(parts[j+1])
                                                if ar_type == 'AR@1':
                                                    metrics['best_ar_1'] = ar_value
                                                elif ar_type == 'AR@5':
                                                    metrics['best_ar_5'] = ar_value
                                                elif ar_type == 'AR@10':
                                                    metrics['best_ar_10'] = ar_value
                                                elif ar_type == 'AR@100':
                                                    metrics['best_ar_100'] = ar_value
                            except (ValueError, IndexError):
                                continue
        except Exception as e:
            print(f"警告: 解析日志文件失败 {log_file}: {e}")
        
        return metrics
    
    def generate_report(self, output_file=None):
        """生成实验报告"""
        experiments = self.list_experiments()
        results = self.get_experiment_results()
        
        # 创建报告
        report = {
            'timestamp': pd.Timestamp.now().isoformat(),
            'total_experiments': len(experiments),
            'completed_experiments': len([r for r in results if r['status'] == 'completed']),
            'running_experiments': len([r for r in results if r['status'] == 'running']),
            'failed_experiments': len([r for r in results if r['status'] == 'failed']),
            'experiments': experiments,
            'results': results
        }
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            print(f"报告已保存到: {output_file}")
        
        return report
    
    def print_summary(self):
        """打印实验摘要"""
        experiments = self.list_experiments()
        results = self.get_experiment_results()
        
        print("=" * 80)
        print("BMN模型优化实验摘要")
        print("=" * 80)
        
        print(f"\n📊 实验统计:")
        print(f"  总实验数: {len(experiments)}")
        print(f"  已完成: {len([r for r in results if r['status'] == 'completed'])}")
        print(f"  运行中: {len([r for r in results if r['status'] == 'running'])}")
        print(f"  失败: {len([r for r in results if r['status'] == 'failed'])}")
        
        print(f"\n🧪 可用实验配置:")
        for exp in experiments:
            print(f"  - {exp['config_name']}: {exp['experiment']}")
        
        print(f"\n📈 实验结果:")
        if results:
            # 按AUC排序
            results_sorted = sorted([r for r in results if r['best_auc'] is not None], 
                                  key=lambda x: x['best_auc'], reverse=True)
            
            for result in results_sorted:
                print(f"  {result['experiment']}:")
                print(f"    状态: {result['status']}")
                if result['best_auc'] is not None:
                    print(f"    最佳AUC: {result['best_auc']:.4f}")
                    print(f"    AR@1: {result['best_ar_1']:.4f}" if result['best_ar_1'] else "    AR@1: N/A")
                    print(f"    AR@5: {result['best_ar_5']:.4f}" if result['best_ar_5'] else "    AR@5: N/A")
                    print(f"    AR@10: {result['best_ar_10']:.4f}" if result['best_ar_10'] else "    AR@10: N/A")
                    print(f"    AR@100: {result['best_ar_100']:.4f}" if result['best_ar_100'] else "    AR@100: N/A")
                print()
        else:
            print("  暂无实验结果")

def main():
    parser = argparse.ArgumentParser(description='BMN实验管理器')
    parser.add_argument('--action', choices=['list', 'results', 'report', 'summary'], 
                        default='summary', help='执行的操作')
    parser.add_argument('--experiment', type=str, help='特定实验名称')
    parser.add_argument('--output', type=str, help='输出文件路径')
    
    args = parser.parse_args()
    
    manager = ExperimentManager()
    
    if args.action == 'list':
        experiments = manager.list_experiments()
        for exp in experiments:
            print(f"{exp['config_name']}: {exp['experiment']}")
    
    elif args.action == 'results':
        results = manager.get_experiment_results(args.experiment)
        for result in results:
            print(json.dumps(result, indent=2, ensure_ascii=False))
    
    elif args.action == 'report':
        report = manager.generate_report(args.output)
        if not args.output:
            print(json.dumps(report, indent=2, ensure_ascii=False))
    
    elif args.action == 'summary':
        manager.print_summary()

if __name__ == '__main__':
    main()
