#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BMN自动化实验脚本测试
测试脚本的各项功能是否正常工作
"""

import os
import sys
import unittest
import tempfile
import shutil
from pathlib import Path

# 添加脚本路径
script_dir = os.path.join(os.path.dirname(__file__), '../data_process/training/scripts')
sys.path.insert(0, os.path.abspath(script_dir))

try:
    from auto_experiment_bmn import BMNExperimentRunner
except ImportError as e:
    print(f"❌ 无法导入BMNExperimentRunner: {e}")
    print(f"   脚本路径: {os.path.abspath(script_dir)}")
    sys.exit(1)

class TestBMNExperimentRunner(unittest.TestCase):
    """测试BMN实验运行器"""
    
    def setUp(self):
        """设置测试环境"""
        self.test_dir = tempfile.mkdtemp()
        self.configs_dir = os.path.join(self.test_dir, "configs")
        self.results_dir = os.path.join(self.test_dir, "results")
        
        # 创建测试目录
        os.makedirs(self.configs_dir)
        os.makedirs(self.results_dir)
        
        # 创建测试配置文件
        self.create_test_config()
        
        # 创建实验运行器
        self.runner = BMNExperimentRunner(
            configs_dir=self.configs_dir,
            results_dir=self.results_dir
        )
    
    def tearDown(self):
        """清理测试环境"""
        shutil.rmtree(self.test_dir)
    
    def create_test_config(self):
        """创建测试配置文件"""
        config_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试实验配置
目标: 测试自动化脚本功能
预期效果: 验证脚本能正确解析配置
"""

# 模型设置
model = dict(
    type='BMN',
    temporal_dim=100,
    boundary_ratio=0.5,
    num_samples=32,
    num_samples_per_bin=3,
    feat_dim=2048,
    soft_nms_alpha=0.4,
    soft_nms_low_threshold=0.5,
    soft_nms_high_threshold=0.9,
    post_process_top_k=100)

# 训练配置
max_epochs = 20
train_cfg = dict(max_epochs=max_epochs, val_interval=2)

# 优化器配置
optim_wrapper = dict(
    optimizer=dict(type='Adam', lr=0.001, weight_decay=1e-4),
    clip_grad=dict(max_norm=40, norm_type=2))

# 工作目录
work_dir = './work_dirs/test_experiment'
'''
        
        config_file = os.path.join(self.configs_dir, "exp_test_config.py")
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(config_content)
    
    def test_log_message(self):
        """测试日志记录功能"""
        test_message = "测试日志消息"
        self.runner.log_message(test_message, print_msg=False)
        
        # 检查日志文件是否创建
        self.assertTrue(os.path.exists(self.runner.log_file))
        
        # 检查日志内容
        with open(self.runner.log_file, 'r', encoding='utf-8') as f:
            content = f.read()
            self.assertIn(test_message, content)
    
    def test_get_experiment_configs(self):
        """测试获取实验配置文件"""
        configs = self.runner.get_experiment_configs()
        
        # 应该找到一个配置文件
        self.assertEqual(len(configs), 1)
        self.assertTrue(configs[0].endswith("exp_test_config.py"))
    
    def test_extract_experiment_info(self):
        """测试提取实验信息"""
        config_file = os.path.join(self.configs_dir, "exp_test_config.py")
        info = self.runner.extract_experiment_info(config_file)
        
        # 检查描述信息
        self.assertIn("测试实验配置", info['description'])
        
        # 检查参数提取
        params = info['parameters']
        self.assertIn('boundary_ratio', params)
        self.assertEqual(params['boundary_ratio'], '0.5')
        self.assertIn('num_samples', params)
        self.assertEqual(params['num_samples'], '32')
        self.assertIn('lr', params)
        self.assertEqual(params['lr'], '0.001')
        self.assertIn('max_epochs', params)
        self.assertEqual(params['max_epochs'], '20')
    
    def test_save_experiment_results(self):
        """测试保存实验结果"""
        test_results = [
            {
                'experiment_name': 'test_exp',
                'config_file': 'test_config.py',
                'status': 'success',
                'final_metrics': {
                    'auc': 1.5,
                    'ar@1': 0.01,
                    'ar@5': 0.05,
                    'ar@10': 0.10
                },
                'duration_minutes': 10.5
            }
        ]
        
        self.runner.save_experiment_results(test_results)
        
        # 检查结果文件是否创建
        self.assertTrue(os.path.exists(self.runner.results_file))
        
        # 检查结果内容
        import json
        with open(self.runner.results_file, 'r', encoding='utf-8') as f:
            saved_results = json.load(f)
            
        self.assertEqual(len(saved_results['results']), 1)
        self.assertEqual(saved_results['results'][0]['experiment_name'], 'test_exp')
        self.assertEqual(saved_results['results'][0]['final_metrics']['auc'], 1.5)


def run_functionality_test():
    """运行功能测试"""
    print("=" * 60)
    print("🧪 BMN自动化实验脚本功能测试")
    print("=" * 60)
    
    # 检查脚本文件是否存在
    script_path = "../data_process/training/scripts/auto_experiment_bmn.py"
    if not os.path.exists(script_path):
        print("❌ 脚本文件不存在:", script_path)
        return False
    
    print("✅ 脚本文件存在")
    
    # 检查配置文件目录
    configs_dir = "../data_process/training/configs"
    if not os.path.exists(configs_dir):
        print("❌ 配置文件目录不存在:", configs_dir)
        return False
    
    print("✅ 配置文件目录存在")
    
    # 检查实验配置文件
    import glob
    config_files = glob.glob(os.path.join(configs_dir, "exp*.py"))
    print(f"✅ 找到 {len(config_files)} 个实验配置文件:")
    for config_file in config_files:
        print(f"   - {os.path.basename(config_file)}")
    
    # 检查README文件
    readme_path = "../data_process/training/scripts/README_auto_experiment.md"
    if os.path.exists(readme_path):
        print("✅ 使用说明文档存在")
    else:
        print("⚠️  使用说明文档不存在")
    
    print("\n💡 测试建议:")
    print("   1. 运行单个实验测试: python auto_experiment_bmn.py --single-config ../configs/exp1_1_data_augmentation.py")
    print("   2. 运行所有实验: python auto_experiment_bmn.py")
    print("   3. 查看帮助信息: python auto_experiment_bmn.py --help")
    
    return True


if __name__ == '__main__':
    # 运行功能测试
    if run_functionality_test():
        print("\n🎉 功能测试通过!")
        
        # 运行单元测试
        print("\n" + "=" * 60)
        print("🧪 运行单元测试")
        print("=" * 60)
        unittest.main(verbosity=2)
    else:
        print("\n❌ 功能测试失败!")
        sys.exit(1)
