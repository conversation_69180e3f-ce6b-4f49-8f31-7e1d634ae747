#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的BMN数据测试脚本
"""

import sys
import os
import json
import numpy as np
import pandas as pd
from pathlib import Path

# 添加mmaction2路径
sys.path.insert(0, str(Path(__file__).parent.parent / "mmaction2"))

def test_data_format():
    """测试数据格式"""
    print("=== 测试数据格式 ===")
    
    # 检查修复后的标注文件
    train_ann = Path("data/MultiClassTAD/multiclass_tad_train_fixed.json")
    val_ann = Path("data/MultiClassTAD/multiclass_tad_val_fixed.json")
    
    if train_ann.exists():
        with open(train_ann, 'r') as f:
            train_data = json.load(f)
        print(f"训练集视频数量: {len(train_data)}")
        
        # 检查第一个视频
        first_video = list(train_data.keys())[0]
        first_info = train_data[first_video]
        print(f"第一个视频: {first_video}")
        print(f"  持续时间: {first_info['duration_second']}秒")
        print(f"  特征帧数: {first_info['feature_frame']}")
        print(f"  FPS: {first_info['fps']}")
        print(f"  标注数量: {len(first_info['annotations'])}")
        
        # 检查视频名称格式
        has_v_prefix = all(name.startswith('v_') for name in train_data.keys())
        print(f"所有视频名称都有v_前缀: {has_v_prefix}")
    
    # 检查特征文件
    features_dir = Path("data/MultiClassTAD/features_slowonly")
    if features_dir.exists():
        feature_files = list(features_dir.glob("*.csv"))
        print(f"\n特征文件数量: {len(feature_files)}")
        
        if feature_files:
            # 检查第一个特征文件
            sample_file = feature_files[0]
            data = pd.read_csv(sample_file)
            print(f"特征文件形状: {data.shape}")
            print(f"数据类型: {data.dtypes.iloc[0]}")
            print(f"数据范围: [{data.values.min():.4f}, {data.values.max():.4f}]")

def test_custom_transform():
    """测试自定义变换"""
    print("\n=== 测试自定义变换 ===")
    
    # 导入自定义变换
    sys.path.insert(0, "data_process/training/configs")
    from fix_transforms import LoadLocalizationFeatureForBMN
    
    # 创建变换实例
    transform = LoadLocalizationFeatureForBMN()
    
    # 测试特征加载
    features_dir = Path("data/MultiClassTAD/features_slowonly")
    feature_files = list(features_dir.glob("*.csv"))
    
    if feature_files:
        test_file = feature_files[0]
        results = {'feature_path': str(test_file)}
        
        # 应用变换
        transformed = transform.transform(results)
        
        if 'raw_feature' in transformed:
            feature = transformed['raw_feature']
            print(f"加载的特征形状: {feature.shape}")
            print(f"特征数据类型: {feature.dtype}")
            print(f"特征数据范围: [{feature.min():.4f}, {feature.max():.4f}]")
            
            # 检查是否是正确的格式 [temporal_dim, feat_dim]
            if feature.shape[0] == 100 and feature.shape[1] == 2048:
                print("✓ 特征格式正确: [temporal_dim=100, feat_dim=2048]")
            else:
                print(f"⚠ 特征格式可能有问题: {feature.shape}")

def main():
    """主函数"""
    print("=== BMN数据格式测试 ===")
    
    try:
        test_data_format()
        test_custom_transform()
        print("\n=== 测试完成 ===")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
