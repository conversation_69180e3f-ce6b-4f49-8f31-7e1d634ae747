#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析三个JSON文件中出现的MP4文件名的脚本

该脚本会：
1. 读取三个JSON文件
2. 提取每个文件中的MP4文件名
3. 分析文件名的分布情况
4. 显示每个文件独有的和共同的MP4文件名
"""

import json
import os
from pathlib import Path
from typing import Dict, Set, List


def load_json_file(file_path: str) -> Dict:
    """加载JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"错误: 找不到文件 {file_path}")
        return {}
    except json.JSONDecodeError as e:
        print(f"错误: 解析JSON文件 {file_path} 失败: {e}")
        return {}


def extract_mp4_filenames(json_data: Dict) -> Set[str]:
    """从JSON数据中提取MP4文件名"""
    mp4_files = set()
    for key in json_data.keys():
        # 添加.mp4扩展名
        mp4_files.add(f"{key}.mp4")
    return mp4_files


def analyze_mp4_files():
    """分析三个JSON文件中的MP4文件名"""
    
    # 定义文件路径
    base_path = Path("data/MultiClassTAD")
    json_files = {
        "train": base_path / "multiclass_tad_train.json",
        "val": base_path / "multiclass_tad_val.json", 
        "full": base_path / "multiclass_tad_full.json"
    }
    
    # 检查文件是否存在
    for name, path in json_files.items():
        if not path.exists():
            print(f"警告: {name} 文件不存在: {path}")
            return
    
    # 加载JSON文件并提取MP4文件名
    mp4_sets = {}
    for name, path in json_files.items():
        print(f"正在处理 {name} 文件: {path}")
        json_data = load_json_file(str(path))
        mp4_sets[name] = extract_mp4_filenames(json_data)
        print(f"  - 找到 {len(mp4_sets[name])} 个MP4文件")
    
    print("\n" + "="*60)
    print("分析结果:")
    print("="*60)
    
    # 显示每个文件中的MP4文件名
    for name, mp4_set in mp4_sets.items():
        print(f"\n{name.upper()} 文件中的MP4文件 ({len(mp4_set)} 个):")
        for mp4_file in sorted(mp4_set):
            print(f"  - {mp4_file}")
    
    # 分析交集和差集
    train_set = mp4_sets.get("train", set())
    val_set = mp4_sets.get("val", set())
    full_set = mp4_sets.get("full", set())
    
    print(f"\n" + "-"*40)
    print("交集和差集分析:")
    print("-"*40)
    
    # 所有文件的并集
    all_mp4_files = train_set | val_set | full_set
    print(f"\n所有MP4文件总数: {len(all_mp4_files)}")
    
    # 三个文件的交集
    common_all = train_set & val_set & full_set
    print(f"\n三个文件共同的MP4文件 ({len(common_all)} 个):")
    for mp4_file in sorted(common_all):
        print(f"  - {mp4_file}")
    
    # 两两交集
    train_val_common = train_set & val_set
    train_full_common = train_set & full_set
    val_full_common = val_set & full_set
    
    print(f"\ntrain和val共同的MP4文件 ({len(train_val_common)} 个):")
    for mp4_file in sorted(train_val_common):
        print(f"  - {mp4_file}")
    
    print(f"\ntrain和full共同的MP4文件 ({len(train_full_common)} 个):")
    for mp4_file in sorted(train_full_common):
        print(f"  - {mp4_file}")
    
    print(f"\nval和full共同的MP4文件 ({len(val_full_common)} 个):")
    for mp4_file in sorted(val_full_common):
        print(f"  - {mp4_file}")
    
    # 每个文件独有的MP4文件
    train_only = train_set - val_set - full_set
    val_only = val_set - train_set - full_set
    full_only = full_set - train_set - val_set
    
    print(f"\n仅在train中的MP4文件 ({len(train_only)} 个):")
    for mp4_file in sorted(train_only):
        print(f"  - {mp4_file}")
    
    print(f"\n仅在val中的MP4文件 ({len(val_only)} 个):")
    for mp4_file in sorted(val_only):
        print(f"  - {mp4_file}")
    
    print(f"\n仅在full中的MP4文件 ({len(full_only)} 个):")
    for mp4_file in sorted(full_only):
        print(f"  - {mp4_file}")
    
    # 验证full是否包含train和val的所有文件
    print(f"\n" + "-"*40)
    print("验证分析:")
    print("-"*40)
    
    train_not_in_full = train_set - full_set
    val_not_in_full = val_set - full_set
    
    print(f"train中但不在full中的文件 ({len(train_not_in_full)} 个):")
    for mp4_file in sorted(train_not_in_full):
        print(f"  - {mp4_file}")
    
    print(f"val中但不在full中的文件 ({len(val_not_in_full)} 个):")
    for mp4_file in sorted(val_not_in_full):
        print(f"  - {mp4_file}")
    
    if len(train_not_in_full) == 0 and len(val_not_in_full) == 0:
        print("\n✓ 验证通过: full文件包含了train和val中的所有MP4文件")
    else:
        print("\n✗ 验证失败: full文件没有包含train和val中的所有MP4文件")
    
    # 生成统计报告
    print(f"\n" + "="*60)
    print("统计报告:")
    print("="*60)
    print(f"Train文件MP4数量: {len(train_set)}")
    print(f"Val文件MP4数量: {len(val_set)}")
    print(f"Full文件MP4数量: {len(full_set)}")
    print(f"总计不重复MP4数量: {len(all_mp4_files)}")
    print(f"三个文件共同MP4数量: {len(common_all)}")


if __name__ == "__main__":
    print("开始分析JSON文件中的MP4文件名...")
    analyze_mp4_files()
    print("\n分析完成!")
