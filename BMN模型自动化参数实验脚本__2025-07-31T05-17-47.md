[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[/] NAME:BMN模型优化实验方案 DESCRIPTION:基于当前训练结果(AUC=2.0429, AR@1/5/10=0.0000)制定系统性的参数调优实验计划，重点解决召回率过低的问题
--[/] NAME:阶段1: 数据增强与批次优化 DESCRIPTION:解决数据集过小(每个epoch只有1个batch)的问题，增加数据增强和批次大小
---[x] NAME:实验1.1: 增加数据增强 DESCRIPTION:添加时间域数据增强技术，包括时间裁剪、缩放和噪声添加
---[ ] NAME:实验1.2: 优化批次大小 DESCRIPTION:将batch_size从4增加到8或使用梯度累积模拟更大的batch
---[ ] NAME:实验1.3: 数据采样策略 DESCRIPTION:优化数据采样策略，增加训练数据的多样性
--[/] NAME:阶段2: 模型结构参数调优 DESCRIPTION:优化BMN模型的核心参数，提高边界检测和时间定位精度
---[ ] NAME:实验2.1: 边界参数优化 DESCRIPTION:
---[ ] NAME:实验2.2: 时间分辨率优化 DESCRIPTION:优化num_samples_per_bin参数，提高时间定位精度
---[ ] NAME:实验2.3: 模型容量调优 DESCRIPTION:调优隐藏层维度，平衡模型容量和过拟合风险
--[ ] NAME:阶段3: 学习率与优化器调优 DESCRIPTION:优化学习率调度策略和优化器参数，提高模型收敛效果
---[ ] NAME:实验3.1: 学习率调度优化 DESCRIPTION:优化学习率初始值和衰减策略，改善模型收敛
---[ ] NAME:实验3.2: 优化器更换 DESCRIPTION:尝试AdamW优化器替代Adam，改善泛化性能
---[ ] NAME:实验3.3: 正则化参数优化 DESCRIPTION:优化weight_decay和梯度裁剪参数，防止过拟合
--[ ] NAME:阶段4: 后处理参数优化 DESCRIPTION:优化NMS阈值和后处理参数，提高检测精度
---[ ] NAME:实验4.1: NMS阈值优化 DESCRIPTION:优化soft_nms阈值参数，提高检测精度
---[ ] NAME:实验4.2: 后处理top-k优化 DESCRIPTION:优化post_process_top_k参数，平衡召回率和精度
---[ ] NAME:实验4.3: 置信度阈值调优 DESCRIPTION:调优检测置信度阈值，改善AR@1/5/10指标
--[ ] NAME:阶段5: 训练策略优化 DESCRIPTION:优化训练轮数、验证频率和模型保存策略
---[ ] NAME:实验5.1: 训练轮数优化 DESCRIPTION:将训练轮数从20增加到30-50轮，充分训练模型
---[ ] NAME:实验5.2: 验证策略优化 DESCRIPTION:优化验证频率和早停策略，防止过拟合
---[ ] NAME:实验5.3: 模型保存策略 DESCRIPTION:优化checkpoint保存策略和模型选择标准